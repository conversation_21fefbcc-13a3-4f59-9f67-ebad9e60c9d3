import React from 'react';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ChatLayout from './ChatLayout';

interface ValidationDataFormProps {
  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component chỉ hiển thị ChatLayout cho validation data
 */
const ValidationDataForm: React.FC<ValidationDataFormProps> = ({ onConversationsChange }) => {
  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    // Notify parent component
    if (onConversationsChange) {
      onConversationsChange(updatedConversations);
    }
  };

  return (
    <div className="h-full">
      {/* Chat Layout Container - Full height */}
      <div className="h-full">
        <ChatLayout onConversationsChange={handleConversationsChange} />
      </div>
    </div>
  );
};

export default ValidationDataForm;
