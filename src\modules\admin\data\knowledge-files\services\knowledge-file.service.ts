import { apiClient } from '@/shared/api/axios';
import {
  ApiResponse,
  CreateKnowledgeFileDto,
  KnowledgeFileListResponse,
  KnowledgeFileQueryParams,
} from '../types';

/**
 * Service để tương tác với API quản lý file tri thức
 */
export class KnowledgeFileService {
  private baseUrl = '/admin';

  /**
   * <PERSON><PERSON>y danh sách file tri thức
   * @param params Tham số truy vấn
   * @returns Danh sách file tri thức và thông tin phân trang
   */
  async getKnowledgeFiles(params?: KnowledgeFileQueryParams): Promise<KnowledgeFileListResponse> {
    try {
      // Xây dựng query string từ params
      const queryParams = new URLSearchParams();
      if (params) {
        if (params.vectorStoreId) queryParams.append('vectorStoreId', params.vectorStoreId);
        if (params.extensions) queryParams.append('extensions', params.extensions);
        if (params.search) queryParams.append('search', params.search);
        if (params.page) queryParams.append('page', params.page.toString());
        if (params.limit) queryParams.append('limit', params.limit.toString());
        if (params.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);
      }

      const queryString = queryParams.toString();
      const url = `${this.baseUrl}/knowledge-files${queryString ? `?${queryString}` : ''}`;

      const response = await apiClient.get<KnowledgeFileListResponse>(url, { tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('[KnowledgeFileService] Error fetching knowledge files:', error);
      throw error;
    }
  }

  /**
   * Tạo nhiều file tri thức mới
   * @param files Danh sách file cần tạo
   * @returns Thông báo kết quả
   */
  async createKnowledgeFiles(files: CreateKnowledgeFileDto[]): Promise<ApiResponse> {
    try {
      const response = await apiClient.post<ApiResponse>(
        `${this.baseUrl}/knowledge-files/batch`,
        { files },
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error('[KnowledgeFileService] Error creating knowledge files:', error);
      throw error;
    }
  }

  /**
   * Xóa một hoặc nhiều file tri thức
   * @param fileIds ID hoặc mảng ID của các file cần xóa
   * @returns Thông báo kết quả
   */
  async deleteKnowledgeFiles(fileIds: string | string[]): Promise<ApiResponse> {
    try {
      // Chuyển đổi fileId thành mảng nếu chỉ truyền vào một ID
      const ids = Array.isArray(fileIds) ? fileIds : [fileIds];

      // Gửi request với body chứa mảng fileIds
      const response = await apiClient.delete<ApiResponse>(`${this.baseUrl}/knowledge-files`, {
        data: { fileIds: ids },
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('[KnowledgeFileService] Error deleting knowledge files:', error);
      throw error;
    }
  }
}

// Tạo instance của service để sử dụng trong toàn ứng dụng
export const knowledgeFileService = new KnowledgeFileService();
