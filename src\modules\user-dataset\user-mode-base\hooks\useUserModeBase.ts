import { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  getUserModeBaseList,
  getUserModeBaseDetail,
  compareModes,
  rateMode,
  getPopularModes,
  getRecommendedModes,
  searchModes,
  getModeUsageStats,
  getModeRatings,
  getAvailableProviders,
} from '../services/user-mode-base.service';
import {
  UserModeBaseQueryDto,
  CompareModesDto,
  RateModeDto,
  UserModeBaseSortBy,
  ModeBaseProvider,
  ModeBaseType,
} from '../types/user-mode-base.types';

/**
 * Query keys cho User Mode Base
 */
export const USER_MODE_BASE_QUERY_KEYS = {
  all: ['user-mode-base'] as const,
  lists: () => [...USER_MODE_BASE_QUERY_KEYS.all, 'list'] as const,
  list: (params: UserModeBaseQueryDto) => [...USER_MODE_BASE_QUERY_KEYS.lists(), params] as const,
  details: () => [...USER_MODE_BASE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...USER_MODE_BASE_QUERY_KEYS.details(), id] as const,
  popular: (limit: number) => [...USER_MODE_BASE_QUERY_KEYS.all, 'popular', limit] as const,
  recommended: (useCase: string, limit: number) =>
    [...USER_MODE_BASE_QUERY_KEYS.all, 'recommended', useCase, limit] as const,
  search: (query: string, limit: number) =>
    [...USER_MODE_BASE_QUERY_KEYS.all, 'search', query, limit] as const,
  usageStats: (id: string) => [...USER_MODE_BASE_QUERY_KEYS.all, 'usage-stats', id] as const,
  ratings: (id: string, page: number, limit: number) =>
    [...USER_MODE_BASE_QUERY_KEYS.all, 'ratings', id, page, limit] as const,
  providers: () => [...USER_MODE_BASE_QUERY_KEYS.all, 'providers'] as const,
  compare: (modeIds: string[]) =>
    [...USER_MODE_BASE_QUERY_KEYS.all, 'compare', ...modeIds.sort()] as const,
};

/**
 * Hook để lấy danh sách mode base
 */
export const useUserModeBaseList = (queryDto?: UserModeBaseQueryDto) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.list(queryDto || {}),
    queryFn: () => getUserModeBaseList(queryDto),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy chi tiết mode base
 */
export const useUserModeBaseDetail = (id: string) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.detail(id),
    queryFn: () => getUserModeBaseDetail(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy modes phổ biến
 */
export const usePopularModes = (limit: number = 10) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.popular(limit),
    queryFn: () => getPopularModes(limit),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook để lấy modes được khuyến nghị
 */
export const useRecommendedModes = (useCase: string, limit: number = 5) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.recommended(useCase, limit),
    queryFn: () => getRecommendedModes(useCase, limit),
    enabled: !!useCase,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook để tìm kiếm modes
 */
export const useSearchModes = (query: string, limit: number = 20) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.search(query, limit),
    queryFn: () => searchModes(query, limit),
    enabled: !!query && query.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thống kê sử dụng mode
 */
export const useModeUsageStats = (id: string) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.usageStats(id),
    queryFn: () => getModeUsageStats(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy đánh giá của mode
 */
export const useModeRatings = (id: string, page: number = 1, limit: number = 10) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.ratings(id, page, limit),
    queryFn: () => getModeRatings(id, page, limit),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy danh sách providers
 */
export const useAvailableProviders = () => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.providers(),
    queryFn: () => getAvailableProviders(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook để so sánh modes
 */
export const useCompareModes = (modeIds: string[]) => {
  return useQuery({
    queryKey: USER_MODE_BASE_QUERY_KEYS.compare(modeIds),
    queryFn: () => compareModes({ modeIds }),
    enabled: modeIds.length >= 2,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để so sánh modes (mutation)
 */
export const useCompareModesMutation = () => {
  return useMutation({
    mutationFn: (data: CompareModesDto) => compareModes(data),
  });
};

/**
 * Hook để đánh giá mode
 */
export const useRateMode = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: RateModeDto }) => rateMode(id, data),
    onSuccess: (_, variables) => {
      // Invalidate detail và ratings
      queryClient.invalidateQueries({
        queryKey: USER_MODE_BASE_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: USER_MODE_BASE_QUERY_KEYS.ratings(variables.id, 1, 10),
      });
      // Invalidate list để cập nhật average rating
      queryClient.invalidateQueries({
        queryKey: USER_MODE_BASE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook tổng hợp để lấy thông tin mode với các dữ liệu liên quan
 */
export const useModeWithDetails = (id: string) => {
  const modeDetail = useUserModeBaseDetail(id);
  const usageStats = useModeUsageStats(id);
  const ratings = useModeRatings(id, 1, 5);

  return {
    mode: modeDetail.data,
    usageStats: usageStats.data,
    ratings: ratings.data,
    isLoading: modeDetail.isLoading || usageStats.isLoading || ratings.isLoading,
    error: modeDetail.error || usageStats.error || ratings.error,
    refetch: () => {
      modeDetail.refetch();
      usageStats.refetch();
      ratings.refetch();
    },
  };
};

/**
 * Hook để lấy modes theo filter nhanh
 */
export const useModesByFilter = (filter: {
  provider?: ModeBaseProvider;
  type?: ModeBaseType;
  supportsFineTuning?: boolean;
  supportsFunctionCalling?: boolean;
  supportsVision?: boolean;
}) => {
  return useUserModeBaseList({
    ...filter,
    limit: 50,
    sortBy: UserModeBaseSortBy.POPULARITY,
    sortDirection: 'DESC',
  });
};

/**
 * Hook để tìm kiếm modes với debounce
 */
export const useDebouncedSearchModes = (query: string, delay: number = 300) => {
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => clearTimeout(timer);
  }, [query, delay]);

  return useSearchModes(debouncedQuery);
};
