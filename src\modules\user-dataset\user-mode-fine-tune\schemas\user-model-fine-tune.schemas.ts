import { z } from 'zod';
import {
  ModelFineTuneStatus,
  ModelFineTuneSortBy,
} from '../types/user-model-fine-tune.types';

/**
 * Schema cho việc tạo model fine tune từ dataset
 */
export const CreateModelFineTuneFromDatasetSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên model không được để trống')
    .max(255, 'Tên model không được vượt quá 255 ký tự'),
  
  baseModelName: z
    .string()
    .min(1, 'Tên base model không được để trống'),
  
  datasetId: z
    .string()
    .uuid('ID dataset không hợp lệ'),
  
  capabilities: z
    .string()
    .optional(),
  
  keyLlmId: z
    .string()
    .optional(),
});

/**
 * Schema cho việc kiểm tra tương thích
 */
export const CheckCompatibilitySchema = z.object({
  datasetId: z
    .string()
    .uuid('ID dataset không hợp lệ'),
  
  baseModelName: z
    .string()
    .min(1, 'Tên base model không được để trống'),
});

/**
 * Schema cho việc test model
 */
export const TestModelSchema = z.object({
  message: z
    .string()
    .min(1, 'Message không được để trống'),
  
  parameters: z
    .object({
      temperature: z
        .number()
        .min(0)
        .max(2)
        .optional(),
      
      max_tokens: z
        .number()
        .int()
        .min(1)
        .max(4096)
        .optional(),
      
      top_p: z
        .number()
        .min(0)
        .max(1)
        .optional(),
      
      frequency_penalty: z
        .number()
        .min(-2)
        .max(2)
        .optional(),
      
      presence_penalty: z
        .number()
        .min(-2)
        .max(2)
        .optional(),
    })
    .optional(),
});

/**
 * Schema cho việc truy vấn danh sách model fine tune
 */
export const UserModelFineTuneQuerySchema = z.object({
  page: z
    .number()
    .int()
    .min(1, 'Số trang phải lớn hơn 0')
    .optional()
    .default(1),
  
  limit: z
    .number()
    .int()
    .min(1, 'Số lượng item phải lớn hơn 0')
    .max(100, 'Số lượng item không được vượt quá 100')
    .optional()
    .default(10),
  
  search: z
    .string()
    .optional(),
  
  status: z
    .nativeEnum(ModelFineTuneStatus, {
      errorMap: () => ({ message: 'Trạng thái không hợp lệ' }),
    })
    .optional(),
  
  baseModelName: z
    .string()
    .optional(),
  
  sortBy: z
    .nativeEnum(ModelFineTuneSortBy, {
      errorMap: () => ({ message: 'Trường sắp xếp không hợp lệ' }),
    })
    .optional()
    .default(ModelFineTuneSortBy.CREATED_AT),
  
  sortDirection: z
    .enum(['ASC', 'DESC'], {
      errorMap: () => ({ message: 'Hướng sắp xếp không hợp lệ' }),
    })
    .optional()
    .default('DESC'),
});

/**
 * Schema cho response của model fine tune
 */
export const UserModelFineTuneResponseSchema = z.object({
  id: z.string().uuid('ID không hợp lệ'),
  name: z.string(),
  baseModelName: z.string(),
  datasetId: z.string().uuid('Dataset ID không hợp lệ'),
  status: z.nativeEnum(ModelFineTuneStatus),
  createdAt: z.number(),
  finishedAt: z.number().optional(),
  providerModelId: z.string().optional(),
  error: z.string().optional(),
});

/**
 * Schema cho response chi tiết của model fine tune
 */
export const UserModelFineTuneDetailResponseSchema = UserModelFineTuneResponseSchema.extend({
  capabilities: z.string().optional(),
  keyLlmId: z.string().optional(),
  dataset: z
    .object({
      id: z.string().uuid(),
      name: z.string(),
      status: z.string(),
    })
    .optional(),
  trainingMetrics: z
    .object({
      loss: z.number().optional(),
      accuracy: z.number().optional(),
      epochs: z.number().optional(),
      learningRate: z.number().optional(),
    })
    .optional(),
  validationMetrics: z
    .object({
      loss: z.number().optional(),
      accuracy: z.number().optional(),
    })
    .optional(),
});

/**
 * Schema cho lịch sử fine tune
 */
export const FineTuneHistoryResponseSchema = z.object({
  id: z.string().uuid('ID không hợp lệ'),
  timestamp: z.number(),
  level: z.enum(['info', 'warning', 'error']),
  message: z.string(),
  data: z.record(z.unknown()).optional(),
});

/**
 * Schema cho trạng thái fine tune
 */
export const FineTuneStatusResponseSchema = z.object({
  status: z.nativeEnum(ModelFineTuneStatus),
  progress: z.number().min(0).max(100).optional(),
  statusDetails: z.string().optional(),
  estimatedCompletionTime: z.number().optional(),
  currentMetrics: z
    .object({
      loss: z.number().optional(),
      accuracy: z.number().optional(),
      epoch: z.number().optional(),
    })
    .optional(),
});

/**
 * Schema cho kiểm tra tương thích
 */
export const CompatibilityCheckResponseSchema = z.object({
  isCompatible: z.boolean(),
  reason: z.string().optional(),
  suggestions: z.array(z.string()).optional(),
  details: z
    .object({
      datasetFormat: z.string().optional(),
      modelRequirements: z.array(z.string()).optional(),
      estimatedTrainingTime: z.number().optional(),
      estimatedCost: z.number().optional(),
    })
    .optional(),
});

/**
 * Schema cho test model
 */
export const TestModelResponseSchema = z.object({
  response: z.string(),
  processingTime: z.number(),
  tokensUsed: z
    .object({
      prompt: z.number(),
      completion: z.number(),
      total: z.number(),
    })
    .optional(),
  metadata: z
    .object({
      model: z.string(),
      temperature: z.number(),
      maxTokens: z.number(),
    })
    .optional(),
});

// Export types inferred from schemas
export type CreateModelFineTuneFromDatasetFormData = z.infer<typeof CreateModelFineTuneFromDatasetSchema>;
export type CheckCompatibilityFormData = z.infer<typeof CheckCompatibilitySchema>;
export type TestModelFormData = z.infer<typeof TestModelSchema>;
export type UserModelFineTuneQueryFormData = z.infer<typeof UserModelFineTuneQuerySchema>;
