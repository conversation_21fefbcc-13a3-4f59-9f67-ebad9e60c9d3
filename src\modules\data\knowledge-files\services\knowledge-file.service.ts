import { apiClient } from '@/shared/api/axios';
import {
  BatchCreateFilesDto,
  BatchCreateFilesResponseDto,
  FileResponseDto,
  PaginatedFileResult,
  QueryFileDto,
} from '../types/knowledge-files.types';

const API_BASE_URL = '/user/knowledge-files';

/**
 * Tạo nhiều file tri thức
 * @param dto Thông tin các file cần tạo
 * @returns Thông tin về việc tạo file thành công và danh sách presigned URLs
 */
export const batchCreateFiles = async (
  dto: BatchCreateFilesDto
): Promise<BatchCreateFilesResponseDto> => {
  const response = await apiClient.post<BatchCreateFilesResponseDto>(`${API_BASE_URL}/batch`, dto);
  return response.result;
};

/**
 * Lấy danh sách file tri thức
 * @param queryDto Tham số truy vấn
 * @returns Danh sách file tri thức với phân trang
 */
export const getFiles = async (queryDto?: QueryFileDto): Promise<PaginatedFileResult> => {
  // Xây dựng query string từ queryDto
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.vectorStoreId) queryParams.append('vectorStoreId', queryDto.vectorStoreId);
  if (queryDto?.extensions) queryParams.append('extensions', queryDto.extensions);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedFileResult>(url);
  return response.result;
};

/**
 * Lấy thông tin chi tiết file tri thức
 * @param id ID của file
 * @returns Thông tin chi tiết file
 */
export const getFileDetail = async (id: string): Promise<FileResponseDto> => {
  const response = await apiClient.get<FileResponseDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Xóa file tri thức
 * @param id ID của file cần xóa
 * @returns Thông tin về việc xóa file thành công
 */
export const deleteFile = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Xóa nhiều file tri thức cùng lúc
 * @param ids Danh sách ID của các file cần xóa
 * @returns Thông tin về việc xóa các file thành công
 */
export const deleteMultipleFiles = async (fileIds: string[]): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/batch`, {
    data: { fileIds },
  });
  return response.result;
};

/**
 * Tải lên file tri thức
 * @param url URL ký sẵn để tải lên
 * @param file File cần tải lên
 * @returns Kết quả tải lên
 */
export const uploadFile = async (url: string, file: File): Promise<void> => {
  // Sử dụng fetch API để tải lên file với phương thức PUT
  await fetch(url, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type,
    },
  });
};

/**
 * Gửi file tri thức để duyệt
 * @param id ID của file cần gửi duyệt
 * @returns Kết quả gửi duyệt
 */
export const submitForApproval = async (
  id: string
): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.put<{ success: boolean; message: string }>(
    `${API_BASE_URL}/${id}/submit`
  );
  return response.result;
};
