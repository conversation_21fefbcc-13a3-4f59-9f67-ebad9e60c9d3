/**
 * Enums cho Model Fine Tune
 */
export enum ModelFineTuneStatus {
  PENDING = 'PENDING',
  VALIDATING_FILES = 'VALIDATING_FILES',
  QUEUED = 'QUEUED',
  RUNNING = 'RUNNING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export enum ModelFineTuneSortBy {
  CREATED_AT = 'createdAt',
  NAME = 'name',
  STATUS = 'status',
}

/**
 * DTO cho việc tạo model fine tune từ dataset và model base
 */
export interface CreateModelFineTuneFromDatasetDto {
  /**
   * Tên model fine-tune
   */
  name: string;

  /**
   * Tên base model được fine-tune
   */
  baseModelName: string;

  /**
   * ID của dataset để fine-tune
   */
  datasetId: string;

  /**
   * Liên kết đến model_registry
   */
  capabilities?: string;

  /**
   * System key dùng để huấn luyện
   */
  keyLlmId?: string;
}

/**
 * DTO cho việc kiểm tra tương thích
 */
export interface CheckCompatibilityDto {
  /**
   * ID của dataset
   */
  datasetId: string;

  /**
   * Tên base model
   */
  baseModelName: string;
}

/**
 * DTO cho việc test model
 */
export interface TestModelDto {
  /**
   * Input message để test
   */
  message: string;

  /**
   * Các tham số test (temperature, max_tokens, etc.)
   */
  parameters?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
  };
}

/**
 * Response cho model fine tune
 */
export interface UserModelFineTuneResponseDto {
  /**
   * ID của model fine tune
   */
  id: string;

  /**
   * Tên model fine tune
   */
  name: string;

  /**
   * Tên base model
   */
  baseModelName: string;

  /**
   * ID của dataset được sử dụng
   */
  datasetId: string;

  /**
   * Trạng thái fine tune
   */
  status: ModelFineTuneStatus;

  /**
   * Thời gian tạo (epoch timestamp)
   */
  createdAt: number;

  /**
   * Thời gian hoàn thành (epoch timestamp)
   */
  finishedAt?: number;

  /**
   * Model ID từ provider (OpenAI, etc.)
   */
  providerModelId?: string;

  /**
   * Thông tin lỗi nếu có
   */
  error?: string;
}

/**
 * Response chi tiết cho model fine tune
 */
export interface UserModelFineTuneDetailResponseDto extends UserModelFineTuneResponseDto {
  /**
   * Capabilities ID
   */
  capabilities?: string;

  /**
   * Key LLM ID
   */
  keyLlmId?: string;

  /**
   * Thông tin dataset
   */
  dataset?: {
    id: string;
    name: string;
    status: string;
  };

  /**
   * Metrics từ quá trình training
   */
  trainingMetrics?: {
    loss?: number;
    accuracy?: number;
    epochs?: number;
    learningRate?: number;
  };

  /**
   * Validation metrics
   */
  validationMetrics?: {
    loss?: number;
    accuracy?: number;
  };
}

/**
 * Response cho lịch sử fine tune
 */
export interface FineTuneHistoryResponseDto {
  /**
   * ID của event
   */
  id: string;

  /**
   * Timestamp của event
   */
  timestamp: number;

  /**
   * Level của log (info, warning, error)
   */
  level: 'info' | 'warning' | 'error';

  /**
   * Message của event
   */
  message: string;

  /**
   * Dữ liệu bổ sung
   */
  data?: Record<string, unknown>;
}

/**
 * Response cho trạng thái fine tune
 */
export interface FineTuneStatusResponseDto {
  /**
   * Trạng thái hiện tại
   */
  status: ModelFineTuneStatus;

  /**
   * Tiến độ (0-100)
   */
  progress?: number;

  /**
   * Thông tin chi tiết trạng thái
   */
  statusDetails?: string;

  /**
   * Thời gian ước tính hoàn thành
   */
  estimatedCompletionTime?: number;

  /**
   * Metrics hiện tại
   */
  currentMetrics?: {
    loss?: number;
    accuracy?: number;
    epoch?: number;
  };
}

/**
 * Response cho kiểm tra tương thích
 */
export interface CompatibilityCheckResponseDto {
  /**
   * Có tương thích không
   */
  isCompatible: boolean;

  /**
   * Lý do không tương thích (nếu có)
   */
  reason?: string;

  /**
   * Các gợi ý để cải thiện tương thích
   */
  suggestions?: string[];

  /**
   * Thông tin chi tiết
   */
  details?: {
    datasetFormat?: string;
    modelRequirements?: string[];
    estimatedTrainingTime?: number;
    estimatedCost?: number;
  };
}

/**
 * Response cho test model
 */
export interface TestModelResponseDto {
  /**
   * Kết quả từ model
   */
  response: string;

  /**
   * Thời gian xử lý (ms)
   */
  processingTime: number;

  /**
   * Số token được sử dụng
   */
  tokensUsed?: {
    prompt: number;
    completion: number;
    total: number;
  };

  /**
   * Metadata bổ sung
   */
  metadata?: {
    model: string;
    temperature: number;
    maxTokens: number;
  };
}

/**
 * Query DTO cho model fine tune
 */
export interface UserModelFineTuneQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Tìm kiếm theo trạng thái
   */
  status?: ModelFineTuneStatus;

  /**
   * Tìm kiếm theo base model
   */
  baseModelName?: string;

  /**
   * Trường sắp xếp
   */
  sortBy?: ModelFineTuneSortBy;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
