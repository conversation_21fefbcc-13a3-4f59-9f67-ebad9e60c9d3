import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';

import { Loading } from '@/shared';
import { ToolsPage, ToolManagementPage } from '../pages';
import MainLayout from '@/shared/layouts/MainLayout';
import IntegrationPage from '../pages/IntegrationPage';

/**
 * Routes cho module tools của user
 */
const toolRoutes: RouteObject[] = [
  // Trang tổng quan quản lý tools
  {
    path: '/tools',
    element: (
      <MainLayout title="Tool Management">
        <Suspense fallback={<Loading />}>
          <ToolManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang danh sách tools
  {
    path: '/tools/list',
    element: (
      <MainLayout title="Tools List">
        <Suspense fallback={<Loading />}>
          <ToolsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  //trang tích hợp tool
  {
    path: '/tools/integrations',
    element: (
      <MainLayout title="Tool Integrations">
        <Suspense fallback={<Loading />}>
          <IntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default toolRoutes;
