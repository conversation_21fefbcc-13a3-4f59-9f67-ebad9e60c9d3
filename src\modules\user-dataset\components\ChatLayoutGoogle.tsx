import React, { useState, useCallback, useEffect } from 'react';
import {
  ImportedConversation,
  DatasetMessage,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ChatPanel from './ChatPanel';
import { useTranslation } from 'react-i18next';

interface ChatLayoutGoogleProps {
  /**
   * Callback khi có thay đổi conversations
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component layout cho Google chat interface - chỉ có chat, không có sidebar
 * Tự động tạo conversation và vào thẳng chat interface
 */
const ChatLayoutGoogle: React.FC<ChatLayoutGoogleProps> = ({ onConversationsChange }) => {
  const { t } = useTranslation();

  // State
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);

  // Get selected conversation
  const selectedConversation = conversations.find(conv => conv.id === selectedConversationId);

  // Generate unique ID
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Generate conversation title from first user message
  const generateTitle = (messages: DatasetMessage[]): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      // Lấy 30 ký tự đầu để title ngắn gọn hơn
      return firstUserMessage.content.length > 30
        ? firstUserMessage.content.substring(0, 30) + '...'
        : firstUserMessage.content;
    }
    return 'Google Chat';
  };

  // Auto-create conversation on mount
  useEffect(() => {
    if (conversations.length === 0) {
      const newConversation: ImportedConversation = {
        id: generateId(),
        title: 'Google Chat',
        messages: [],
        createdAt: new Date(),
      };

      setConversations([newConversation]);
      setSelectedConversationId(newConversation.id);
      onConversationsChange?.([newConversation]);
    }
  }, [conversations.length, onConversationsChange]);

  // Handle add message to selected conversation
  const handleAddMessage = useCallback(
    (message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages, message];

            // Cập nhật title nếu đây là message đầu tiên và là user message
            let newTitle = conv.title;
            if (conv.messages.length === 0 && message.role === 'user') {
              newTitle = generateTitle([message]);
            }

            return {
              ...conv,
              title: newTitle,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle delete message from selected conversation
  const handleDeleteMessage = useCallback(
    (messageIndex: number) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];
            newMessages.splice(messageIndex, 1);
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle edit message in selected conversation
  const handleEditMessage = useCallback(
    (messageIndex: number, message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];
            newMessages[messageIndex] = message;
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  return (
    <div className="h-full w-full">
      {/* Direct Chat Interface - No Sidebar */}
      {selectedConversation ? (
        <ChatPanel
          title={selectedConversation.title}
          messages={selectedConversation.messages}
          onAddMessage={handleAddMessage}
          onDeleteMessage={handleDeleteMessage}
          onEditMessage={handleEditMessage}
          placeholder="Nhập tin nhắn cho Google AI..."
        />
      ) : (
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-3">💬</div>
            <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
              {t('Bắt đầu cuộc trò chuyện')}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-sm max-w-sm">
              {t('Thêm tin nhắn để tạo dataset huấn luyện')}
            </p>
            <div className="mt-4">
              <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                Google Provider
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatLayoutGoogle;
