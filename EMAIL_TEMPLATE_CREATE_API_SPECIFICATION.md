# Email Template Create API Specification

## Overview
API specification cho tính năng tạo Email Template trong hệ thống RedAI Marketing.

## Endpoint
```
POST /api/v1/marketing/template-emails
```

## Authentication
- **Required**: JW<PERSON>
- **Guard**: JwtUserGuard
- **User Context**: <PERSON><PERSON><PERSON> từ @CurrentUser() decorator

## Request DTO

### CreateTemplateEmailDto

```typescript
export class CreateTemplateEmailDto {
  /**
   * Tên template
   * @example "Newsletter tháng 1"
   */
  @ApiProperty({
    description: 'Tên template',
    example: 'Newsletter tháng 1',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  /**
   * Tiêu đề email
   * @example "🎉 Khuyến mãi đặc biệt dành cho bạn!"
   */
  @ApiProperty({
    description: 'Tiêu đề email',
    example: '🎉 Khuyến mãi đặc biệt dành cho bạn!',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  subject: string;

  /**
   * Nội dung HTML của email
   * @example "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>"
   */
  @ApiProperty({
    description: 'Nội dung HTML của email',
    example: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  /**
   * Nội dung text thuần (tùy chọn)
   * @example "Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter..."
   */
  @ApiProperty({
    description: 'Nội dung text thuần (tùy chọn)',
    example: 'Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter...',
    required: false,
  })
  @IsOptional()
  @IsString()
  textContent?: string;

  /**
   * Loại template
   * @example "NEWSLETTER"
   */
  @ApiProperty({
    description: 'Loại template',
    example: 'NEWSLETTER',
    enum: ['NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP'])
  type?: string;

  /**
   * Preview text hiển thị trong inbox
   * @example "Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm..."
   */
  @ApiProperty({
    description: 'Preview text hiển thị trong inbox',
    example: 'Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  previewText?: string;

  /**
   * Danh sách tags
   * @example ["newsletter", "promotion", "monthly"]
   */
  @ApiProperty({
    description: 'Danh sách tags',
    example: ['newsletter', 'promotion', 'monthly'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  /**
   * Danh sách biến động trong template
   * @example [{"name": "customer_name", "type": "TEXT", "defaultValue": "Khách hàng", "required": true, "description": "Tên khách hàng"}]
   */
  @ApiProperty({
    description: 'Danh sách biến động trong template',
    example: [
      {
        name: 'customer_name',
        type: 'TEXT',
        defaultValue: 'Khách hàng',
        required: true,
        description: 'Tên khách hàng'
      }
    ],
    type: [Object],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailVariableDto)
  variables?: EmailVariableDto[];
}
```

### EmailVariableDto

```typescript
export class EmailVariableDto {
  /**
   * Tên biến (không có dấu cách, chỉ chữ cái, số và _)
   * @example "customer_name"
   */
  @ApiProperty({
    description: 'Tên biến (không có dấu cách, chỉ chữ cái, số và _)',
    example: 'customer_name',
    pattern: '^[a-zA-Z_][a-zA-Z0-9_]*$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z_][a-zA-Z0-9_]*$/, {
    message: 'Tên biến chỉ được chứa chữ cái, số và dấu gạch dưới, bắt đầu bằng chữ cái hoặc _'
  })
  name: string;

  /**
   * Loại dữ liệu của biến
   * @example "TEXT"
   */
  @ApiProperty({
    description: 'Loại dữ liệu của biến',
    example: 'TEXT',
    enum: ['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE'],
  })
  @IsEnum(['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE'])
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';

  /**
   * Giá trị mặc định
   * @example "Khách hàng"
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: 'Khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString()
  defaultValue?: string;

  /**
   * Biến có bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Biến có bắt buộc hay không',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  /**
   * Mô tả biến
   * @example "Tên khách hàng sẽ được hiển thị trong email"
   */
  @ApiProperty({
    description: 'Mô tả biến',
    example: 'Tên khách hàng sẽ được hiển thị trong email',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}
```

## Response DTO

### TemplateEmailResponseDto

```typescript
export class TemplateEmailResponseDto {
  /**
   * ID của template
   * @example 1
   */
  @ApiProperty({
    description: 'ID của template',
    example: 1,
  })
  id: number;

  /**
   * ID của user tạo template
   * @example 1
   */
  @ApiProperty({
    description: 'ID của user tạo template',
    example: 1,
  })
  userId: number;

  /**
   * Tên template
   * @example "Newsletter tháng 1"
   */
  @ApiProperty({
    description: 'Tên template',
    example: 'Newsletter tháng 1',
  })
  name: string;

  /**
   * Tiêu đề email
   * @example "🎉 Khuyến mãi đặc biệt dành cho bạn!"
   */
  @ApiProperty({
    description: 'Tiêu đề email',
    example: '🎉 Khuyến mãi đặc biệt dành cho bạn!',
  })
  subject: string;

  /**
   * Nội dung HTML
   * @example "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>"
   */
  @ApiProperty({
    description: 'Nội dung HTML',
    example: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1></body></html>',
  })
  content: string;

  /**
   * Nội dung text thuần
   * @example "Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter..."
   */
  @ApiProperty({
    description: 'Nội dung text thuần',
    example: 'Xin chào {customer_name}! Cảm ơn bạn đã đăng ký newsletter...',
    required: false,
  })
  textContent?: string;

  /**
   * Loại template
   * @example "NEWSLETTER"
   */
  @ApiProperty({
    description: 'Loại template',
    example: 'NEWSLETTER',
    required: false,
  })
  type?: string;

  /**
   * Trạng thái template
   * @example "DRAFT"
   */
  @ApiProperty({
    description: 'Trạng thái template',
    example: 'DRAFT',
    enum: ['DRAFT', 'ACTIVE', 'ARCHIVED'],
  })
  status: 'DRAFT' | 'ACTIVE' | 'ARCHIVED';

  /**
   * Preview text
   * @example "Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm..."
   */
  @ApiProperty({
    description: 'Preview text',
    example: 'Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...',
    required: false,
  })
  previewText?: string;

  /**
   * Danh sách tags
   * @example ["newsletter", "promotion", "monthly"]
   */
  @ApiProperty({
    description: 'Danh sách tags',
    example: ['newsletter', 'promotion', 'monthly'],
    type: [String],
  })
  tags: string[];

  /**
   * Danh sách placeholders (tên biến)
   * @example ["customer_name", "product_name", "discount_amount"]
   */
  @ApiProperty({
    description: 'Danh sách placeholders (tên biến)',
    example: ['customer_name', 'product_name', 'discount_amount'],
    type: [String],
  })
  placeholders: string[];

  /**
   * Metadata của biến (chi tiết)
   * @example {"customer_name": {"type": "TEXT", "defaultValue": "Khách hàng", "required": true, "description": "Tên khách hàng"}}
   */
  @ApiProperty({
    description: 'Metadata của biến (chi tiết)',
    example: {
      customer_name: {
        type: 'TEXT',
        defaultValue: 'Khách hàng',
        required: true,
        description: 'Tên khách hàng'
      }
    },
    required: false,
  })
  variableMetadata?: Record<string, {
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  }>;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1640995200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1640995200000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;
}
```

## API Response Format

### Success Response (201 Created)

```json
{
  "code": 201,
  "message": "Tạo template email thành công",
  "result": {
    "id": 1,
    "userId": 1,
    "name": "Newsletter tháng 1",
    "subject": "🎉 Khuyến mãi đặc biệt dành cho bạn!",
    "content": "<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1><p>Chúng tôi có ưu đãi đặc biệt cho {product_name} với giảm giá {discount_amount}%!</p></body></html>",
    "textContent": "Xin chào {customer_name}! Chúng tôi có ưu đãi đặc biệt cho {product_name} với giảm giá {discount_amount}%!",
    "type": "NEWSLETTER",
    "status": "DRAFT",
    "previewText": "Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...",
    "tags": ["newsletter", "promotion", "monthly"],
    "placeholders": ["customer_name", "product_name", "discount_amount"],
    "variableMetadata": {
      "customer_name": {
        "type": "TEXT",
        "defaultValue": "Khách hàng",
        "required": true,
        "description": "Tên khách hàng sẽ được hiển thị trong email"
      },
      "product_name": {
        "type": "TEXT",
        "defaultValue": "sản phẩm",
        "required": false,
        "description": "Tên sản phẩm khuyến mãi"
      },
      "discount_amount": {
        "type": "NUMBER",
        "defaultValue": "10",
        "required": false,
        "description": "Phần trăm giảm giá"
      }
    },
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  }
}
```

### Error Responses

#### 400 Bad Request - Validation Error

```json
{
  "code": 400,
  "message": "Dữ liệu không hợp lệ",
  "errors": [
    {
      "field": "name",
      "message": "Tên template là bắt buộc"
    },
    {
      "field": "subject",
      "message": "Tiêu đề email là bắt buộc"
    },
    {
      "field": "content",
      "message": "Nội dung HTML là bắt buộc"
    },
    {
      "field": "variables[0].name",
      "message": "Tên biến chỉ được chứa chữ cái, số và dấu gạch dưới"
    }
  ]
}
```

#### 401 Unauthorized

```json
{
  "code": 401,
  "message": "Không có quyền truy cập",
  "error": "Unauthorized"
}
```

#### 409 Conflict - Duplicate Name

```json
{
  "code": 409,
  "message": "Template với tên này đã tồn tại",
  "error": "TEMPLATE_NAME_EXISTS"
}
```

#### 500 Internal Server Error

```json
{
  "code": 500,
  "message": "Lỗi hệ thống",
  "error": "Internal Server Error"
}
```

## Business Logic Requirements

### 1. Validation Rules

#### Template Name
- **Required**: Bắt buộc
- **Length**: 1-255 ký tự
- **Unique**: Phải unique trong scope của user
- **Trim**: Tự động trim whitespace

#### Subject
- **Required**: Bắt buộc
- **Length**: 1-255 ký tự
- **Trim**: Tự động trim whitespace

#### Content (HTML)
- **Required**: Bắt buộc
- **Validation**: Kiểm tra HTML cơ bản (không cần strict)
- **Placeholder Detection**: Tự động extract placeholders từ content

#### Variables
- **Name Validation**: Chỉ chứa a-z, A-Z, 0-9, _ và bắt đầu bằng chữ cái hoặc _
- **Unique Names**: Tên biến phải unique trong template
- **Type Validation**: Phải thuộc enum ['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE']

#### Tags
- **Optional**: Không bắt buộc
- **Trim**: Tự động trim whitespace cho mỗi tag
- **Filter Empty**: Loại bỏ tags rỗng

### 2. Auto-Processing

#### Placeholder Extraction
```typescript
// Tự động extract placeholders từ content
// Pattern: {variable_name}
const extractPlaceholders = (content: string): string[] => {
  const regex = /\{([a-zA-Z_][a-zA-Z0-9_]*)\}/g;
  const matches = [];
  let match;
  while ((match = regex.exec(content)) !== null) {
    matches.push(match[1]);
  }
  return [...new Set(matches)]; // Remove duplicates
};
```

#### Variable Metadata Processing
```typescript
// Chuyển đổi variables array thành metadata object
const processVariableMetadata = (variables: EmailVariableDto[]): Record<string, any> => {
  const metadata = {};
  variables.forEach(variable => {
    metadata[variable.name] = {
      type: variable.type,
      defaultValue: variable.defaultValue,
      required: variable.required || false,
      description: variable.description
    };
  });
  return metadata;
};
```

### 3. Default Values

- **Status**: Mặc định là 'DRAFT'
- **Type**: Mặc định là 'NEWSLETTER' nếu không được cung cấp
- **Tags**: Mặc định là array rỗng []
- **Variables**: Mặc định là array rỗng []
- **Required (Variable)**: Mặc định là false

### 4. Database Schema Suggestions

```sql
-- Template table
CREATE TABLE template_emails (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  text_content TEXT,
  type ENUM('NEWSLETTER', 'PROMOTIONAL', 'TRANSACTIONAL', 'WELCOME', 'ABANDONED_CART', 'FOLLOW_UP') DEFAULT 'NEWSLETTER',
  status ENUM('DRAFT', 'ACTIVE', 'ARCHIVED') DEFAULT 'DRAFT',
  preview_text VARCHAR(255),
  tags JSON, -- Array of strings
  placeholders JSON, -- Array of strings
  variable_metadata JSON, -- Object with variable details
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_user_template_name (user_id, name),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_type (type),
  INDEX idx_created_at (created_at)
);
```

## Controller Implementation Example

```typescript
@ApiTags(SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/template-emails')
export class UserTemplateEmailController {
  constructor(private readonly userTemplateEmailService: UserTemplateEmailService) {}

  @Post()
  @ApiOperation({
    summary: 'Tạo template email mới',
    description: 'Tạo template email mới với đầy đủ thông tin bao gồm nội dung HTML, biến động và tags'
  })
  @ApiResponse({
    status: 201,
    description: 'Template email được tạo thành công',
    type: TemplateEmailResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ'
  })
  @ApiResponse({
    status: 409,
    description: 'Template với tên này đã tồn tại'
  })
  async createTemplate(
    @CurrentUser() user: JwtPayload,
    @Body() createTemplateDto: CreateTemplateEmailDto,
  ): Promise<ApiResponseDto<TemplateEmailResponseDto>> {
    const result = await this.userTemplateEmailService.createTemplate(user.id, createTemplateDto);
    return ApiResponseDto.success(result, 'Tạo template email thành công');
  }
}
```

## Frontend Integration Notes

### Current Frontend Data Structure
Frontend hiện tại gửi data với cấu trúc:
```typescript
{
  name: string;
  subject: string;
  htmlContent: string; // Backend expect: content
  textContent?: string;
  type: EmailTemplateType;
  previewText?: string;
  variables: EmailVariable[];
  tags: string[];
}
```

### Mapping Required
- `htmlContent` (frontend) → `content` (backend)
- `variables` array → `placeholders` array + `variableMetadata` object

### Adapter Service Update
Cần cập nhật `EmailTemplateAdapterService.createEmailTemplate()` để map đúng fields.
