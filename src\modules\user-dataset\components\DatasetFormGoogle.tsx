import React, { useState } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';
import ChatLayoutGoogle from './ChatLayoutGoogle';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';

// Schema validation cho Google dataset form
const GoogleDatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
});

type GoogleDatasetFormData = z.infer<typeof GoogleDatasetFormSchema>;

interface DatasetFormGoogleProps {
  /**
   * Callback khi tạo dataset thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component form tạo dataset cho Google với ConversationSidebarGoogle
 * Giao diện: ChatLayoutGoogle + ConversationSidebarGoogle + ChatPanel + DatasetForm
 * Không có import file JSON
 */
const DatasetFormGoogle: React.FC<DatasetFormGoogleProps> = ({
  onSuccess,
  onConversationsChange,
}) => {
  const { t } = useTranslation();
  const { createAndUpload, isLoading } = useCreateAndUploadDataset();

  // State cho conversations và dataset
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<GoogleDatasetFormData>({
    resolver: zodResolver(GoogleDatasetFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    // Notify parent component
    if (onConversationsChange) {
      onConversationsChange(updatedConversations);
    }
  };

  // Xử lý submit form
  const onSubmit = async (data: GoogleDatasetFormData) => {
    try {
      if (conversations.length === 0) {
        console.error('No conversations available');
        return;
      }

      // Convert conversations to JSONL format
      const trainJsonlData = convertConversationsToJsonl(conversations);

      // Validate JSONL data
      const validation = validateJsonlData(trainJsonlData);
      if (!validation.isValid) {
        console.error('Training data validation failed:', validation.errors);
        return;
      }

      // Create dataset info for Google
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: data.name,
        description: data.description,
        provider: ProviderFineTuneEnum.GOOGLE,
        trainDataset: 'application/jsonl',
        validDataset: undefined, // Google không cần validation data
      };

      // Create and upload dataset
      await createAndUpload({
        datasetInfo,
        trainJsonlData,
        validJsonlData: undefined, // Google không cần validation data
      });

      // Reset form và notify success
      reset();
      setConversations([]);
      setShowForm(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating Google dataset:', error);
    }
  };

  return (
    <div>
      {/* Form Header */}
      {showForm && (
        <Card>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 mb-4">
              <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                <Input
                  {...register('name')}
                  placeholder={t('Nhập tên dataset cho Google')}
                  error={errors.name?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('Description')}
                helpText={errors.description?.message}
                required
              >
                <Textarea
                  {...register('description')}
                  placeholder={t('Nhập mô tả dataset cho Google AI')}
                  status={errors.description?.message ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>

            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {conversations.length > 0 && (
                  <span>{conversations.length} conversations • Google Provider</span>
                )}
              </div>

              <div className="flex space-x-2 ">
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  disabled={conversations.length === 0}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {t('Tạo Dataset Google')}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Chat Layout Container */}
      <div className="relative">
        <div className="h-[600px] ">
          <ChatLayoutGoogle onConversationsChange={handleConversationsChange} />
        </div>

        {/* Create Dataset Button */}
        {conversations.length > 0 && !showForm && (
          <div className="absolute top-4 right-4 ">
            <Button
              onClick={() => setShowForm(true)}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              {t('Tạo Dataset Google')} ({conversations.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatasetFormGoogle;
