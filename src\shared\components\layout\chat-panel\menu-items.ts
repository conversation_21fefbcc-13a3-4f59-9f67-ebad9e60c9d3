import { ModernMenuItem } from './ModernMenu';
import { AuthType } from '@/shared/hooks/useAuthCommon';

/**
 * Menu items cho người dùng thông thường (user)
 */
export const userMenuItems: ModernMenuItem[] = [
  {
    id: 'home',
    label: 'common:home',
    path: '/',
    icon: 'home',
    keywords: ['trang chủ', 'home', 'main', 'dashboard', 'trang chinh'],
  },
  {
    id: 'ai-agents',
    label: 'aiAgents:aiAgents',
    path: '/ai-agents',
    icon: 'components',
    keywords: [
      'ai agents',
      'ai agent',
      'aiagent',
      'aiagents',
      'agent',
      'agents',
      'bot',
      'bots',
      'trợ lý',
      'tro ly',
    ],
  },
  {
    id: 'integrations',
    label: 'integrations:title',
    path: '/integrations',
    icon: 'integration',
    keywords: ['integration', 'integrations', 'tích hợp', 'tich hop', 'kết nối', 'ket noi'],
  },
  {
    id: 'calendar',
    label: 'calendar:title',
    path: '/calendar',
    icon: 'calendar',
    keywords: [
      'calendar',
      'lịch',
      'lich',
      'sự kiện',
      'su kien',
      'event',
      'events',
      'cuộc họp',
      'cuoc hop',
      'meeting',
      'schedule',
      'lịch trình',
      'lich trinh',
    ],
  },
  {
    id: 'marketing',
    label: 'marketing:title',
    path: '/marketing',
    icon: 'campaign',
    keywords: [
      'marketing',
      'chiến dịch',
      'chien dich',
      'campaign',
      'campaigns',
      'quảng cáo',
      'quang cao',
      'ads',
      'email marketing',
      'sms marketing',
      'promotion',
      'khuyến mãi',
      'khuyen mai',
    ],
  },
  {
    id: 'data',
    label: 'data:title',
    path: '/data',
    icon: 'chart',
    keywords: [
      'data',
      'dữ liệu',
      'du lieu',
      'analytics',
      'phân tích',
      'phan tich',
      'thống kê',
      'thong ke',
      'báo cáo',
      'bao cao',
      'report',
      'reports',
      'dashboard',
      'chart',
      'charts',
      'biểu đồ',
      'bieu do',
    ],
  },
  {
    id: 'marketplace',
    label: 'marketplace:title',
    path: '/marketplace',
    icon: 'shopping-cart',
    keywords: [
      'marketplace',
      'market',
      'shop',
      'store',
      'cửa hàng',
      'cua hang',
      'sản phẩm',
      'san pham',
      'products',
      'mua sắm',
      'mua sam',
      'shopping',
    ],
  },
  {
    id: 'business',
    label: 'business:title',
    path: '/business',
    icon: 'business',
    keywords: ['payment', 'thương mại', 'thuong mai', 'thanh toán', 'kinh doanh'],
  },
  {
    id: 'rpoint-packages',
    label: 'rpoint:packages.title',
    path: '/rpoint/packages',
    icon: 'money',
    keywords: [
      'rpoint',
      'r-point',
      'point',
      'points',
      'gói point',
      'goi point',
      'điểm',
      'diem',
      'nạp điểm',
      'nap diem',
    ],
  },
  {
    id: 'subscription-packages',
    label: 'subscription:title',
    path: '/subscription/packages',
    icon: 'subscription',
    keywords: [
      'subscription',
      'gói dịch vụ',
      'goi dich vu',
      'dịch vụ',
      'dich vu',
      'đăng ký',
      'dang ky',
      'service',
      'services',
      'packages',
    ],
  },
  {
    id: 'affiliate',
    label: 'affiliate:title',
    path: '/user/affiliate',
    icon: 'link',
    keywords: [
      'affiliate',
      'tiếp thị liên kết',
      'tiep thi lien ket',
      'cộng tác viên',
      'cong tac vien',
      'đối tác',
      'doi tac',
      'hoa hồng',
      'hoa hong',
      'commission',
    ],
  },
  {
    id: 'settings',
    label: 'common:settings',
    path: '/settings',
    icon: 'settings',
    keywords: ['settings', 'cài đặt', 'cai dat', 'thiết lập', 'thiet lap', 'config'],
  },
  {
    id: 'profile',
    label: 'common:profile',
    path: '/profile',
    icon: 'user',
    keywords: ['profile', 'hồ sơ', 'ho so', 'tài khoản', 'tai khoan', 'account', 'user'],
  },
  {
    id: 'help',
    label: 'common:help',
    path: '/help',
    icon: 'info',
    keywords: [
      'help',
      'trợ giúp',
      'tro giup',
      'hỗ trợ',
      'ho tro',
      'support',
      'hướng dẫn',
      'huong dan',
    ],
  },
  {
    id: 'tools',
    label: 'tools:title',
    path: '/tools',
    icon: 'settings',
    keywords: ['tools', 'công cụ', 'cong cu', 'utility', 'tiện ích', 'tien ich'],
  },
];

/**
 * Menu items cho quản trị viên (admin)
 */
export const adminMenuItems: ModernMenuItem[] = [
  {
    id: 'admin-dashboard',
    label: 'admin:dashboard',
    path: '/admin',
    icon: 'home',
    keywords: ['admin', 'dashboard', 'trang quản trị', 'trang quan tri', 'quản trị', 'quan tri'],
  },
  {
    id: 'admin-users',
    label: 'admin:users',
    path: '/admin/users-page',
    icon: 'user',
    keywords: ['users', 'người dùng', 'nguoi dung', 'khách hàng', 'khach hang', 'customers'],
  },
  {
    id: 'admin-employees',
    label: 'admin:employees',
    path: '/admin/employees',
    icon: 'users',
    keywords: [
      'employees',
      'nhân viên',
      'nhan vien',
      'staff',
      'quản lý nhân viên',
      'quan ly nhan vien',
    ],
  },
  {
    id: 'admin-affiliate',
    label: 'admin:affiliate',
    path: '/admin/affiliate',
    icon: 'link',
    keywords: ['affiliate', 'đối tác', 'doi tac', 'cộng tác viên', 'cong tac vien', 'partners'],
  },
  {
    id: 'admin-rpoint',
    label: 'admin:rpoint',
    path: '/admin/r-point',
    icon: 'rpoint',
    keywords: ['rpoint', 'r-point', 'point', 'points', 'quản lý điểm', 'quan ly diem'],
  },
  {
    id: 'admin-marketplace',
    label: 'admin:marketplace',
    path: '/admin/marketplace',
    icon: 'marketplace',
    keywords: ['marketplace', 'Sàn thương mại', 'san thuong mai'],
  },
  {
    id: 'admin-subscription',
    label: 'admin:subscription',
    path: '/admin/subscription',
    icon: 'subscription',
    keywords: ['subscription', 'gói dịch vụ', 'goi dich vu', 'quản lý dịch vụ', 'quan ly dich vu'],
  },
  {
    id: 'admin-settings',
    label: 'admin:settings',
    path: '/admin/settings',
    icon: 'settings',
    keywords: ['settings', 'cài đặt', 'cai dat', 'thiết lập', 'thiet lap', 'config'],
  },
  {
    id: 'admin-data',
    label: 'admin:data',
    path: '/admin/data',
    icon: 'chart',
    keywords: [
      'media',
      'hình ảnh',
      'hinh anh',
      'video',
      'file',
      'files',
      'tài nguyên',
      'tai nguyen',
    ],
  },
  {
    id: 'business',
    label: 'business:title',
    path: '/admin/business',
    icon: 'business',
    keywords: ['payment', 'thương mại', 'thuong mai', 'thanh toán', 'kinh doanh'],
  },
  {
    id: 'admin-tools',
    label: 'admin:tools',
    path: '/admin/tools',
    icon: 'settings',
    keywords: ['tools', 'công cụ', 'cong cu', 'quản lý công cụ', 'quan ly cong cu'],
  },
];

/**
 * Lấy menu items dựa trên loại người dùng
 * @param authType Loại người dùng (user, admin, none)
 * @returns Danh sách menu items phù hợp
 */
export const getMenuItemsByAuthType = (authType: AuthType): ModernMenuItem[] => {
  switch (authType) {
    case AuthType.USER:
      return userMenuItems;
    case AuthType.ADMIN:
      return adminMenuItems;
    default:
      // Trường hợp chưa đăng nhập, trả về danh sách trống hoặc menu items cho khách
      return [];
  }
};
