import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * Props cho OverviewCard component
 */
export interface OverviewCardProps {
  /**
   * Tiêu đề của card
   */
  title: string;

  /**
   * Giá trị chính hiển thị
   */
  value: string | number;

  /**
   * <PERSON><PERSON> tả phụ hiển thị dưới value
   */
  description?: string;

  /**
   * Icon hiển thị ở góc phải
   */
  icon?: LucideIcon;

  /**
   * M<PERSON>u sắc chủ đạo của card
   * @default 'blue'
   */
  color?: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'gray';

  /**
   * Trạng thái loading
   * @default false
   */
  isLoading?: boolean;

  /**
   * Class name bổ sung
   */
  className?: string;

  /**
   * Callback khi click vào card
   */
  onClick?: () => void;

  /**
   * Có thể hover được không
   * @default false
   */
  hoverable?: boolean;

  /**
   * Custom content thay thế value
   */
  customValue?: ReactNode;
}

/**
 * Props cho ListOverviewCard component
 */
export interface ListOverviewCardProps {
  /**
   * Danh sách dữ liệu cho các card
   */
  items: OverviewCardProps[];

  /**
   * Số cột tối đa cho mỗi breakpoint
   * @default { xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }
   */
  maxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Khoảng cách giữa các card
   * @default 4
   */
  gap?: number;

  /**
   * Class name bổ sung cho container
   */
  className?: string;

  /**
   * Trạng thái loading cho toàn bộ danh sách
   * @default false
   */
  isLoading?: boolean;

  /**
   * Số lượng skeleton hiển thị khi loading
   * @default 4
   */
  skeletonCount?: number;
}
