import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createModelFineTuneFromDataset,
  checkCompatibility,
  getUserModelFineTuneList,
  getUserModelFineTuneDetail,
  getFineTuneHistory,
  getFineTuneStatus,
  cancelFineTune,
  deleteUserModelFineTune,
  testModel,
  getAvailableBaseModels,
  getBaseModelInfo,
  estimateFineTuneCost,
} from '../services/user-model-fine-tune.service';
import {
  CreateModelFineTuneFromDatasetDto,
  CheckCompatibilityDto,
  TestModelDto,
  UserModelFineTuneQueryDto,
} from '../types/user-model-fine-tune.types';

/**
 * Query keys cho User Model Fine Tune
 */
export const USER_MODEL_FINE_TUNE_QUERY_KEYS = {
  all: ['user-model-fine-tune'] as const,
  lists: () => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'list'] as const,
  list: (params: UserModelFineTuneQueryDto) => 
    [...USER_MODEL_FINE_TUNE_QUERY_KEYS.lists(), params] as const,
  details: () => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.details(), id] as const,
  history: (id: string) => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'history', id] as const,
  status: (id: string) => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'status', id] as const,
  baseModels: () => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'base-models'] as const,
  baseModel: (name: string) => [...USER_MODEL_FINE_TUNE_QUERY_KEYS.baseModels(), name] as const,
  compatibility: (datasetId: string, baseModelName: string) => 
    [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'compatibility', datasetId, baseModelName] as const,
  costEstimate: (datasetId: string, baseModelName: string) => 
    [...USER_MODEL_FINE_TUNE_QUERY_KEYS.all, 'cost-estimate', datasetId, baseModelName] as const,
};

/**
 * Hook để lấy danh sách model fine tune
 */
export const useUserModelFineTuneList = (queryDto?: UserModelFineTuneQueryDto) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.list(queryDto || {}),
    queryFn: () => getUserModelFineTuneList(queryDto),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết model fine tune
 */
export const useUserModelFineTuneDetail = (id: string) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.detail(id),
    queryFn: () => getUserModelFineTuneDetail(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy lịch sử fine tune
 */
export const useFineTuneHistory = (id: string) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.history(id),
    queryFn: () => getFineTuneHistory(id),
    enabled: !!id,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Auto refetch every 30 seconds
  });
};

/**
 * Hook để lấy trạng thái fine tune
 */
export const useFineTuneStatus = (id: string) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.status(id),
    queryFn: () => getFineTuneStatus(id),
    enabled: !!id,
    staleTime: 10 * 1000, // 10 seconds
    refetchInterval: 10 * 1000, // Auto refetch every 10 seconds
  });
};

/**
 * Hook để lấy danh sách base models
 */
export const useAvailableBaseModels = () => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.baseModels(),
    queryFn: () => getAvailableBaseModels(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook để lấy thông tin base model
 */
export const useBaseModelInfo = (modelName: string) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.baseModel(modelName),
    queryFn: () => getBaseModelInfo(modelName),
    enabled: !!modelName,
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook để kiểm tra tương thích
 */
export const useCheckCompatibility = (datasetId: string, baseModelName: string) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.compatibility(datasetId, baseModelName),
    queryFn: () => checkCompatibility({ datasetId, baseModelName }),
    enabled: !!datasetId && !!baseModelName,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để ước tính chi phí
 */
export const useEstimateFineTuneCost = (datasetId: string, baseModelName: string) => {
  return useQuery({
    queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.costEstimate(datasetId, baseModelName),
    queryFn: () => estimateFineTuneCost(datasetId, baseModelName),
    enabled: !!datasetId && !!baseModelName,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo model fine tune từ dataset
 */
export const useCreateModelFineTuneFromDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateModelFineTuneFromDatasetDto) => 
      createModelFineTuneFromDataset(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.lists() 
      });
    },
  });
};

/**
 * Hook để hủy fine tune
 */
export const useCancelFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => cancelFineTune(id),
    onSuccess: (_, id) => {
      // Invalidate danh sách và detail
      queryClient.invalidateQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.lists() 
      });
      queryClient.invalidateQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.detail(id) 
      });
      queryClient.invalidateQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.status(id) 
      });
    },
  });
};

/**
 * Hook để xóa model fine tune
 */
export const useDeleteUserModelFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteUserModelFineTune(id),
    onSuccess: (_, id) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.lists() 
      });
      
      // Remove từ cache
      queryClient.removeQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.detail(id) 
      });
      queryClient.removeQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.history(id) 
      });
      queryClient.removeQueries({ 
        queryKey: USER_MODEL_FINE_TUNE_QUERY_KEYS.status(id) 
      });
    },
  });
};

/**
 * Hook để test model
 */
export const useTestModel = () => {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: TestModelDto }) =>
      testModel(id, data),
  });
};

/**
 * Hook để kiểm tra tương thích (mutation)
 */
export const useCheckCompatibilityMutation = () => {
  return useMutation({
    mutationFn: (data: CheckCompatibilityDto) => checkCompatibility(data),
  });
};

/**
 * Hook để ước tính chi phí (mutation)
 */
export const useEstimateFineTuneCostMutation = () => {
  return useMutation({
    mutationFn: ({ datasetId, baseModelName }: { datasetId: string; baseModelName: string }) =>
      estimateFineTuneCost(datasetId, baseModelName),
  });
};
