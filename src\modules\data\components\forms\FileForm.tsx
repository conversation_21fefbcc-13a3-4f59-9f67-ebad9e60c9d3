import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  FormArray,
  Chip,
  Card,
  FileDisplay,
} from '@/shared/components/common';
import { z } from 'zod';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload from '@/modules/data/components/MultiFileUpload';

// Schema cho form tạo media mới

export type CreateMediaFormValues = {
  files: Array<{
    id: string;
    file: File;
    name: string;
    description?: string; // Mô tả là optional
    tags?: string;
  }>;
};

interface FileFormProps {
  onSubmit: (values: CreateMediaFormValues) => void;
  onCancel: () => void;
  isUploading?: boolean;
  uploadProgress?: number;
}

/**
 * Form component for uploading files
 */
const FileForm: React.FC<FileFormProps> = ({ onSubmit, onCancel, isUploading = false }) => {
  const { t } = useTranslation();
  const [tempTags, setTempTags] = useState<Record<string, string[]>>({});

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = useRef<FormRef<any>>(null);

  // Schema với validation messages sử dụng i18n
  const createMediaSchema = z.object({
    files: z
      .array(
        z.object({
          id: z.string(),
          file: z.custom<File>(val => val instanceof File, {
            message: t('data:files.form.invalidFile', 'File không hợp lệ'),
          }),
          name: z.string().min(1, t('data:files.form.nameRequired', 'Tên tài liệu là bắt buộc')),
          description: z
            .string()
            .min(1, t('data:files.form.descriptionRequired', 'Mô tả là bắt buộc')),
          tags: z.string().optional(),
        })
      )
      .min(1, t('data:files.form.fileRequired', 'Vui lòng chọn ít nhất một file')),
  });

  return (
    <Card className="p-4 sm:p-6 w-full max-w-none">
      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={createMediaSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={onSubmit as any}
        className="space-y-4 w-full"
      >
        <FormItem name="files">
          <MultiFileUpload
            label={t('data:files.form.files')}
            placeholder={t('data:files.form.dragAndDrop')}
            onChange={files => {
              if (!formRef.current) return;

              // Tạo mảng các file với metadata
              const filesWithMetadata = files.map(fileData => ({
                id: fileData.id,
                file: fileData.file,
                name: fileData.file.name, // Tự động lấy tên từ tên file
                description: '',
                tags: '',
              }));

              // Cập nhật giá trị vào form
              formRef.current
                .getFormMethods()
                .setValue('files', filesWithMetadata, { shouldValidate: true });
            }}
            required
          />
        </FormItem>

        {/* Form Array để chỉnh sửa metadata của từng file */}
        <FormArray
          name="files"
          controls={['remove', 'move']}
          className="bg-white dark:bg-gray-900 rounded-lg w-full"
          itemsContainerClassName="grid grid-cols-1 gap-4 md:gap-6 w-full"
          renderItem={index => {
            // Lấy thông tin file từ form
            const fileData = formRef.current?.getFormMethods().getValues(`files.${index}`);

            return (
              <div className="space-y-4 p-4 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 w-full min-w-0">
                {/* Hiển thị file */}
                {fileData && (
                  <div className="mb-4 w-full overflow-hidden">
                    <FileDisplay
                      fileName={fileData.file.name}
                      fileSize={fileData.file.size}
                      className="bg-gray-50 dark:bg-gray-800 rounded-lg border-0 w-full min-w-0"
                    />
                  </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
                  <FormItem
                    name={`files.${index}.name`}
                    label={t('data:files.form.name', 'Tên tài liệu')}
                    required
                  >
                    <Input fullWidth />
                  </FormItem>

                  <FormItem
                    name={`files.${index}.description`}
                    label={t('data:files.form.description')}
                  >
                    <Input fullWidth placeholder={t('data:files.form.descriptionPlaceholder')} />
                  </FormItem>
                </div>

                <FormItem name={`files.${index}.tags`} label={t('data:files.form.tags', 'Tags')}>
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('data:files.form.tagsPlaceholder', 'Nhập tag và nhấn Enter')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();

                          // Lấy tags hiện tại từ state
                          const fileId = formRef.current
                            ?.getFormMethods()
                            .getValues(`files.${index}.id`);
                          if (!fileId) return;

                          // Lấy tags hiện tại hoặc tạo mảng rỗng
                          const currentTempTags = tempTags[fileId] || [];
                          const newTag = e.currentTarget.value.trim();

                          // Thêm tag mới nếu chưa tồn tại
                          if (!currentTempTags.includes(newTag)) {
                            setTempTags(prev => ({
                              ...prev,
                              [fileId]: [...currentTempTags, newTag],
                            }));
                          }

                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {(() => {
                        const fileId = formRef.current
                          ?.getFormMethods()
                          .getValues(`files.${index}.id`);
                        if (!fileId) return null;

                        const currentTempTags = tempTags[fileId] || [];
                        return currentTempTags.map((tag, tagIndex) => (
                          <Chip
                            key={`${index}-${tagIndex}-${tag}`}
                            size="sm"
                            closable
                            onClose={() => {
                              setTempTags(prev => ({
                                ...prev,
                                [fileId]: prev[fileId].filter(t => t !== tag),
                              }));
                            }}
                          >
                            {tag}
                          </Chip>
                        ));
                      })()}
                    </div>
                  </div>
                </FormItem>

                {/* Thêm trường viewUrl ẩn */}
                <FormItem name={`files.${index}.viewUrl`} className="hidden">
                  <Input type="hidden" />
                </FormItem>
              </div>
            );
          }}
        />

        <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-2 pt-4 w-full">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isUploading}
            className="w-full sm:w-auto"
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isUploading}
            className="w-full sm:w-auto"
          >
            {isUploading ? t('common:upload') : t('common:upload')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default FileForm;
