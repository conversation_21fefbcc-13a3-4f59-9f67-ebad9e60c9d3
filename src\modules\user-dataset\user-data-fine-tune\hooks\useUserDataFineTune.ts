import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createUserDataFineTune,
  updateUploadStatus,
  getUserDataFineTuneList,
  getUserDataFineTuneDetail,
  updateUserDataFineTune,
  deleteUserDataFineTune,
  getUploadUrl,
  uploadFile,
  uploadJsonlData,
} from '../services/user-data-fine-tune.service';
import {
  CreateUserDataFineTuneDto,
  UpdateUserDataFineTuneDto,
  UserDataFineTuneQueryDto,
} from '../types/user-data-fine-tune.types';

/**
 * Query keys cho User Data Fine Tune
 */
export const USER_DATA_FINE_TUNE_QUERY_KEYS = {
  all: ['user-data-fine-tune'] as const,
  lists: () => [...USER_DATA_FINE_TUNE_QUERY_KEYS.all, 'list'] as const,
  list: (params: UserDataFineTuneQueryDto) =>
    [...USER_DATA_FINE_TUNE_QUERY_KEYS.lists(), params] as const,
  details: () => [...USER_DATA_FINE_TUNE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...USER_DATA_FINE_TUNE_QUERY_KEYS.details(), id] as const,
  uploadUrl: (mime: string) => [...USER_DATA_FINE_TUNE_QUERY_KEYS.all, 'upload-url', mime] as const,
};

/**
 * Hook để lấy danh sách dataset fine tune
 */
export const useUserDataFineTuneList = (queryDto?: UserDataFineTuneQueryDto) => {
  return useQuery({
    queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.list(queryDto || {}),
    queryFn: () => getUserDataFineTuneList(queryDto),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết dataset fine tune
 */
export const useUserDataFineTuneDetail = (id: string) => {
  return useQuery({
    queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(id),
    queryFn: () => getUserDataFineTuneDetail(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy upload URL
 */
export const useUploadUrl = (mime: string) => {
  return useQuery({
    queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.uploadUrl(mime),
    queryFn: () => getUploadUrl(mime),
    enabled: !!mime,
    staleTime: 0, // Always fresh
  });
};

/**
 * Hook để tạo dataset fine tune
 */
export const useCreateUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserDataFineTuneDto) => createUserDataFineTune(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật dataset fine tune
 */
export const useUpdateUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserDataFineTuneDto }) =>
      updateUserDataFineTune(id, data),
    onSuccess: (data, variables) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });

      // Update cache cho detail
      queryClient.setQueryData(USER_DATA_FINE_TUNE_QUERY_KEYS.detail(variables.id), data);
    },
  });
};

/**
 * Hook để xóa dataset fine tune
 */
export const useDeleteUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteUserDataFineTune(id),
    onSuccess: (_, id) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });

      // Remove từ cache
      queryClient.removeQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(id),
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái upload
 */
export const useUpdateUploadStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: boolean }) => updateUploadStatus(id, status),
    onSuccess: (_, variables) => {
      // Invalidate danh sách và detail
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(variables.id),
      });
    },
  });
};

/**
 * Hook để upload file
 */
export const useUploadFile = () => {
  return useMutation({
    mutationFn: ({ uploadUrl, file }: { uploadUrl: string; file: File }) =>
      uploadFile(uploadUrl, file),
  });
};

/**
 * Hook để upload JSONL data
 */
export const useUploadJsonlData = () => {
  return useMutation({
    mutationFn: ({ uploadUrl, jsonlData }: { uploadUrl: string; jsonlData: string }) =>
      uploadJsonlData(uploadUrl, jsonlData),
  });
};

/**
 * Hook tổng hợp cho việc tạo và upload dataset
 */
export const useCreateAndUploadDataset = () => {
  const createMutation = useCreateUserDataFineTune();
  const uploadJsonlMutation = useUploadJsonlData();
  const updateStatusMutation = useUpdateUploadStatus();

  const createAndUpload = async ({
    datasetInfo,
    trainJsonlData,
    validJsonlData,
  }: {
    datasetInfo: CreateUserDataFineTuneDto;
    trainJsonlData: string;
    validJsonlData?: string;
  }) => {
    try {
      // 1. Tạo dataset và lấy upload URLs
      const createResponse = await createMutation.mutateAsync(datasetInfo);

      // 2. Upload training data
      await uploadJsonlMutation.mutateAsync({
        uploadUrl: createResponse.trainUploadUrl,
        jsonlData: trainJsonlData,
      });

      // 3. Upload validation data nếu có
      if (validJsonlData && createResponse.validUploadUrl) {
        await uploadJsonlMutation.mutateAsync({
          uploadUrl: createResponse.validUploadUrl,
          jsonlData: validJsonlData,
        });
      }

      // 4. Cập nhật trạng thái upload thành công
      await updateStatusMutation.mutateAsync({
        id: createResponse.id,
        status: true,
      });

      return createResponse;
    } catch (error) {
      // Nếu có lỗi, cập nhật trạng thái upload thất bại
      if (createMutation.data?.id) {
        try {
          await updateStatusMutation.mutateAsync({
            id: createMutation.data.id,
            status: false,
          });
        } catch (statusError) {
          console.error('Failed to update status after error:', statusError);
        }
      }
      throw error;
    }
  };

  return {
    createAndUpload,
    isLoading:
      createMutation.isPending || uploadJsonlMutation.isPending || updateStatusMutation.isPending,
    error: createMutation.error || uploadJsonlMutation.error || updateStatusMutation.error,
  };
};
