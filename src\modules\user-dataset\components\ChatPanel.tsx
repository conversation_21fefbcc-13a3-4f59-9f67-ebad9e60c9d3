import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Typography, Tooltip, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { DatasetMessage } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { Send, User, Bot, Settings, Code2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ChatPanelProps {
  /**
   * Tiêu đề của panel
   */
  title: string;

  /**
   * Danh sách message
   */
  messages: DatasetMessage[];

  /**
   * Callback khi thêm message
   */
  onAddMessage: (message: DatasetMessage) => void;

  /**
   * Callback khi xóa message
   */
  onDeleteMessage: (index: number) => void;

  /**
   * Callback khi chỉnh sửa message
   */
  onEditMessage?: (index: number, message: DatasetMessage) => void;

  /**
   * Placeholder cho input
   */
  placeholder?: string;
}

/**
 * Component hiển thị panel chat cho dataset
 */
const ChatPanel: React.FC<ChatPanelProps> = ({
  title,
  messages,
  onAddMessage,
  onDeleteMessage,
  onEditMessage,
  placeholder = 'Nhập tin nhắn...',
}) => {
  const { t } = useTranslation();
  const [role, setRole] = useState<'system' | 'user' | 'assistant'>('user');
  const [content, setContent] = useState('');
  const [showRoleMenu, setShowRoleMenu] = useState(false);

  // State cho edit message
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingContent, setEditingContent] = useState('');

  // State cho tool selection
  const [showToolSelection, setShowToolSelection] = useState(false);
  const [selectedMessageIndex, setSelectedMessageIndex] = useState<number | null>(null);
  const [messageTools, setMessageTools] = useState<Record<number, string[]>>({});

  // Danh sách role options
  const roleOptions = useMemo(
    () => [
      {
        value: 'system',
        label: t('System Role'),
        icon: <Settings size={16} className="mr-2" />,
        color: 'text-gray-500 dark:text-gray-400',
      },
      {
        value: 'user',
        label: t('User Role'),
        icon: <User size={16} className="mr-2" />,
        color: 'text-blue-500 dark:text-blue-400',
      },
      {
        value: 'assistant',
        label: t('Assistant Role'),
        icon: <Bot size={16} className="mr-2" />,
        color: 'text-green-500 dark:text-green-400',
      },
    ],
    [t]
  );

  // Xử lý khi gửi message
  const handleSendMessage = () => {
    if (content.trim()) {
      onAddMessage({
        role,
        content,
      });
      setContent('');

      // Đặt focus lại vào textarea sau khi gửi
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  };

  // Xử lý khi nhấn Enter hoặc /
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Gửi tin nhắn khi nhấn Enter (không phải Shift+Enter)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }

    // Xử lý khi nhấn / để mở menu
    if (e.key === '/' && content === '') {
      e.preventDefault();
      // Đây là nơi sẽ mở menu, nhưng hiện tại chỉ log ra console
      console.log('Open menu triggered');
    }
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Adjust textarea height based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 150)}px`;
    }
  }, [content]);

  // Đặt focus vào textarea khi component mount
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  // Handlers cho edit message
  const handleStartEdit = (index: number, message: DatasetMessage) => {
    setEditingIndex(index);
    setEditingContent(message.content);
  };

  const handleSaveEdit = (index: number) => {
    if (editingContent.trim() && onEditMessage) {
      const updatedMessage: DatasetMessage = {
        role: messages[index].role,
        content: editingContent.trim(),
      };
      onEditMessage(index, updatedMessage);
    }
    setEditingIndex(null);
    setEditingContent('');
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditingContent('');
  };

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>, index: number) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit(index);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  // Mock data cho available tools
  const availableTools = [
    { id: '1', name: 'Get Product Info', description: 'Lấy thông tin sản phẩm' },
    { id: '2', name: 'Get Product Price', description: 'Lấy giá sản phẩm' },
    { id: '3', name: 'Search Products', description: 'Tìm kiếm sản phẩm' },
    { id: '4', name: 'Check Inventory', description: 'Kiểm tra tồn kho' },
    { id: '5', name: 'Calculate Discount', description: 'Tính toán giảm giá' },
  ];

  // Handlers cho tool selection
  const handleShowToolSelection = (index: number) => {
    setSelectedMessageIndex(index);
    setShowToolSelection(true);
  };

  const handleSelectTool = (_toolId: string, toolName: string) => {
    if (selectedMessageIndex !== null) {
      // Chỉ thêm tool vào chip list, không tạo message mới
      const currentTools = messageTools[selectedMessageIndex] || [];
      if (!currentTools.includes(toolName)) {
        setMessageTools(prev => ({
          ...prev,
          [selectedMessageIndex]: [...currentTools, toolName],
        }));
      }
    }

    setShowToolSelection(false);
    setSelectedMessageIndex(null);
  };

  const handleCloseToolSelection = () => {
    setShowToolSelection(false);
    setSelectedMessageIndex(null);
  };

  // Tạo một component con để hiển thị tin nhắn
  const MessageItem = ({ message, index }: { message: DatasetMessage; index: number }) => {
    // Xác định vị trí hiển thị dựa trên role
    const isUser = message.role === 'user';
    const isAssistant = message.role === 'assistant';
    const isSystem = message.role === 'system';

    // Xác định màu nền dựa trên role
    const messageBgColor = isUser
      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-gray-900 dark:text-gray-100'
      : isAssistant
        ? 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100'
        : 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-gray-900 dark:text-gray-100';

    // Xác định icon dựa trên role
    const roleIcon = roleOptions.find(option => option.value === message.role)?.icon;

    // Tạo action items cho message
    const messageActionItems: ActionMenuItem[] = [
      {
        id: 'edit',
        label: t('Chỉnh sửa'),
        icon: 'edit',
        onClick: () => handleStartEdit(index, message),
      },
      {
        id: 'import-tools',
        label: t('Import Tools JSON'),
        icon: 'code',
        onClick: () => handleShowToolSelection(index),
      },
      {
        id: 'delete',
        label: t('Xóa tin nhắn'),
        icon: 'trash',
        onClick: () => onDeleteMessage(index),
      },
    ];

    return (
      <div
        className={`flex items-start gap-3 mb-4 w-full ${isUser ? 'justify-end' : isSystem ? 'justify-center' : 'justify-start'}`}
      >
        {/* Avatar cho assistant - bên trái */}
        {isAssistant && (
          <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
            <Bot size={18} className="text-green-600 dark:text-green-400" />
          </div>
        )}

        {/* Message content */}
        <div
          className={`flex flex-col gap-1 ${isUser ? 'items-end' : 'items-start'} ${isUser ? 'max-w-[70%]' : isSystem ? 'max-w-[90%]' : 'max-w-[70%]'}`}
        >
          <div
            className={`px-4 py-3 rounded-2xl ${messageBgColor} ${isUser ? 'rounded-br-md' : isAssistant ? 'rounded-bl-md' : ''}`}
          >
            {/* Role indicator chỉ hiển thị cho system message */}
            {isSystem && (
              <div className="flex items-center text-xs font-medium mb-2 text-yellow-600 dark:text-yellow-400">
                {roleIcon}
                <span className="ml-1">System</span>
              </div>
            )}
            {/* Conditional rendering: Edit mode vs Display mode */}
            {editingIndex === index ? (
              // Edit mode
              <div className="space-y-2">
                <textarea
                  value={editingContent}
                  onChange={e => setEditingContent(e.target.value)}
                  onKeyDown={e => handleEditKeyDown(e, index)}
                  className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  rows={3}
                  placeholder="Nhập nội dung tin nhắn..."
                  autoFocus
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleSaveEdit(index)}
                    className="px-3 py-1 text-xs bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                    disabled={!editingContent.trim()}
                  >
                    Lưu
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="px-3 py-1 text-xs bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                  >
                    Hủy
                  </button>
                </div>
              </div>
            ) : (
              // Display mode
              <div
                className="whitespace-pre-wrap break-words overflow-hidden"
                style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}
              >
                {message.content}
              </div>
            )}
          </div>

          {/* Tool chips - hiển thị tools đã chọn */}
          {messageTools[index] && messageTools[index].length > 0 && (
            <div
              className={`mt-2 flex flex-wrap gap-1 ${isUser ? 'justify-end' : 'justify-start'}`}
            >
              {messageTools[index].map((toolName, toolIndex) => (
                <div
                  key={toolIndex}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700"
                >
                  <Code2 size={12} className="mr-1" />
                  {toolName}
                </div>
              ))}
            </div>
          )}

          <div className={`flex items-center ${isUser ? 'justify-end' : 'justify-start'}`}>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {new Date().toLocaleTimeString()}
            </span>
            <div className="ml-2">
              <ActionMenu
                items={messageActionItems}
                menuTooltip={t('Thêm thao tác')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                showAllInMenu={true}
                preferRight={true}
              />
            </div>
          </div>
        </div>

        {/* Avatar cho user - bên phải */}
        {isUser && (
          <div className="w-8 h-8 rounded-full overflow-hidden bg-blue-100 dark:bg-blue-900/30 flex-shrink-0 flex items-center justify-center">
            <User size={18} className="text-blue-600 dark:text-blue-400" />
          </div>
        )}

        {/* Avatar cho system - giữa */}
        {isSystem && (
          <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0 flex items-center justify-center">
            <Settings size={18} className="text-gray-600 dark:text-gray-400" />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header - fixed at top */}
      <div className="flex items-center justify-between px-4 pt-4 pb-2 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <Typography variant="h6" className="flex items-center">
          <span className="mr-2">{title}</span>
        </Typography>
      </div>

      {/* Messages area - scrollable */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto px-4 py-4 hide-scrollbar">
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <div className="text-4xl mb-4">💬</div>
              <h2 className="text-xl font-semibold mb-2">{t('Bắt đầu cuộc trò chuyện')}</h2>
              <p className="text-gray-500 dark:text-gray-400 max-w-md">
                {t('Thêm tin nhắn để tạo dataset huấn luyện')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => (
                <MessageItem key={index} message={message} index={index} />
              ))}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input area - fixed at bottom */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3 flex-shrink-0">
        <div className="relative flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full chat-input-box-container">
          <div className="w-full px-3 py-3">
            <textarea
              ref={textareaRef}
              value={content}
              onChange={e => setContent(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={t(placeholder)}
              className="w-full bg-transparent border-0 focus:ring-0 focus:outline-none dark:text-white text-gray-800 resize-none max-h-[150px] custom-scrollbar"
              rows={1}
            />
          </div>

          {/* Action buttons row */}
          <div className="flex items-center px-2 py-2 space-x-1 border-t border-gray-100 dark:border-gray-700">
            {/* Role selector button */}
            <div className="relative">
              <Tooltip content={t('Chọn role cho tin nhắn')} position="top">
                <button
                  className="p-2 w-10 h-10 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors flex-shrink-0"
                  aria-label={t('Chọn role')}
                  onClick={() => setShowRoleMenu(!showRoleMenu)}
                >
                  <div className="w-6 h-6 flex items-center justify-center">
                    {role === 'assistant' && <Bot size={20} className="text-green-500" />}
                    {role === 'user' && <User size={20} className="text-blue-500" />}
                    {role === 'system' && <Settings size={20} className="text-gray-500" />}
                  </div>
                </button>
              </Tooltip>

              {/* Role selection menu */}
              {showRoleMenu && (
                <div className="absolute left-0 bottom-full mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-20 w-56">
                  <div className="p-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-2 px-2 font-medium">
                      {t('Chọn role cho tin nhắn')}
                    </div>
                    {roleOptions.map(option => (
                      <button
                        key={option.value}
                        className={`w-full text-left px-3 py-2 rounded-md transition-colors flex items-center ${
                          role === option.value
                            ? 'bg-primary/10 text-primary dark:bg-primary/20'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                        onClick={() => {
                          setRole(option.value as 'system' | 'user' | 'assistant');
                          setShowRoleMenu(false);
                          if (textareaRef.current) {
                            textareaRef.current.focus();
                          }
                        }}
                      >
                        {option.icon}
                        <span className={option.color}>{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex-grow"></div>

            {/* Send button */}
            <Tooltip content={t('Gửi tin nhắn')} position="top">
              <button
                onClick={handleSendMessage}
                disabled={!content.trim()}
                className={`p-2 w-10 h-10 flex items-center justify-center rounded-full transition-colors ${
                  content.trim()
                    ? 'text-white bg-primary hover:bg-primary/90'
                    : 'text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-100 dark:bg-gray-700'
                }`}
                aria-label={t('Gửi')}
              >
                <Send size={18} />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* Tool Selection Modal */}
      {showToolSelection && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('Chọn Tool')}
                </h3>
                <button
                  onClick={handleCloseToolSelection}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  ✕
                </button>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t('Chọn tool để tạo function calling format')}
              </p>
            </div>

            <div className="p-4 max-h-96 overflow-y-auto">
              <div className="space-y-2">
                {availableTools.map(tool => (
                  <button
                    key={tool.id}
                    onClick={() => handleSelectTool(tool.id, tool.name)}
                    className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center">
                      <Code2 size={16} className="text-blue-500 mr-3" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{tool.name}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {tool.description}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatPanel;
