import React, { useState } from 'react';
import { Typography, Pagination, Loading, Button } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { useUserDataFineTuneList } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { DatasetGrid, ProviderSelectionModal } from '../components';
import {
  DataFineTuneStatus,
  UserDataFineTuneQueryDto,
  UserDataFineTuneSortBy,
  UserDataFineTuneResponseDto,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

// Interface cho backend response structure
interface BackendDatasetResponse {
  items: UserDataFineTuneResponseDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Data Fine-tune Page - <PERSON><PERSON>n thị danh sách dataset fine-tune
 */
const DataFineTunePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [queryParams, setQueryParams] = useState<UserDataFineTuneQueryDto>({
    page: 1,
    limit: 12,
    search: '',
    status: undefined,
    sortBy: UserDataFineTuneSortBy.CREATED_AT,
  });

  // State cho Provider Selection Modal
  const [showProviderModal, setShowProviderModal] = useState(false);

  const { data, isLoading, error } = useUserDataFineTuneList(queryParams);

  // Type assertion để match với backend response
  const backendData = data as unknown as BackendDatasetResponse;

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  const handleCreateNew = () => {
    // Hiển thị Provider Selection Modal
    setShowProviderModal(true);
  };

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page (sẽ tạo sau)
    console.log('Navigate to dataset detail:', id);
  };

  // Handle provider selection
  const handleSelectOpenAI = () => {
    setShowProviderModal(false);
    // Navigate to OpenAI DatasetForm (existing)
    navigate('/user-dataset/create-openai');
  };

  const handleSelectGoogle = () => {
    setShowProviderModal(false);
    // Navigate to Google DatasetForm (new)
    navigate('/user-dataset/create-google');
  };

  const handleCloseProviderModal = () => {
    setShowProviderModal(false);
  };

  // Menu items for filter
  const filterMenuItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: t('Tất cả trạng thái'),
      onClick: () => {
        setQueryParams(prev => ({
          ...prev,
          status: undefined,
          page: 1,
        }));
      },
    },
    ...Object.values(DataFineTuneStatus).map(status => ({
      id: status,
      label: getStatusLabel(status),
      onClick: () => {
        setQueryParams(prev => ({
          ...prev,
          status,
          page: 1,
        }));
      },
    })),
  ];

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleCreateNew}
          items={filterMenuItems}
          showDateFilter={false}
          showColumnFilter={false}
          isLoading={isLoading}
        />

        {/* Loading */}
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        {/* Error */}
        <div className="text-center py-12">
          <Typography variant="body1" className="text-red-600 mb-4">
            {t('Có lỗi xảy ra khi tải dữ liệu')}
          </Typography>
          <Button variant="outline" onClick={() => window.location.reload()}>
            {t('Thử lại')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleCreateNew}
        items={filterMenuItems}
        showDateFilter={false}
        showColumnFilter={false}
        isLoading={isLoading}
      />

      {/* Dataset Grid */}
      {backendData?.items && backendData.items.length > 0 ? (
        <>
          <DatasetGrid
            datasets={backendData.items}
            onViewDataset={dataset => handleSelectDataset(dataset.id)}
            onEditDataset={dataset => {
              // Navigate to edit dataset page (sẽ tạo sau)
              console.log('Navigate to edit dataset:', dataset.id);
            }}
            onDeleteDataset={dataset => {
              // Handle delete dataset (sẽ tạo sau)
              console.log('Delete dataset:', dataset.id);
            }}
          />

          {/* Pagination */}
          {backendData.meta.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <Pagination
                variant="simple"
                currentPage={backendData.meta.currentPage}
                totalPages={backendData.meta.totalPages}
                onPageChange={handlePageChange}
                showItemsPerPageSelector={false}
              />
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <Typography variant="body1" color="muted" className="text-lg">
            {t('Không tìm thấy dataset nào')}
          </Typography>
        </div>
      )}

      {/* Provider Selection Modal */}
      <ProviderSelectionModal
        isOpen={showProviderModal}
        onClose={handleCloseProviderModal}
        onSelectOpenAI={handleSelectOpenAI}
        onSelectGoogle={handleSelectGoogle}
      />
    </div>
  );
};

export default DataFineTunePage;
