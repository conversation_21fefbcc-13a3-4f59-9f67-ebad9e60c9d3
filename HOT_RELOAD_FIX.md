# Sửa Lỗi Hot Reload - Vite Configuration

## Vấn đề
Khi chạy `npm run dev`, thay đổi code không tự động cập nhật giao diện, phải F5 để thấy thay đổi.

## Nguyên nhân
Trong file `vite.config.ts`, cấu hình `hmr: false` đã tắt hoàn toàn Hot Module Replacement (HMR).

## Giải pháp đã áp dụng

### 1. Bật lại HMR với cấu hình tối ưu
```typescript
hmr: {
  port: 24678, // Sử dụng port cụ thể cho HMR
  host: 'localhost',
},
```

### 2. Thêm file watching với polling
```typescript
watch: {
  usePolling: true, // Sử dụng polling để theo dõi thay đổi file
  interval: 100, // Kiểm tra thay đổi mỗi 100ms
},
```

### 3. Bật Fast Refresh cho React
```typescript
react({
  jsxRuntime: 'automatic',
  fastRefresh: true, // Bật Fast Refresh cho React
  babel: {
    plugins: [
      ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
    ]
  }
}),
```

## Cách kiểm tra

1. **Dừng server hiện tại** (nếu đang chạy):
   ```bash
   Ctrl + C
   ```

2. **Khởi động lại dev server**:
   ```bash
   npm run dev
   ```

3. **Kiểm tra hot reload**:
   - Mở browser và truy cập ứng dụng
   - Thay đổi một component React (ví dụ: thay đổi text)
   - Lưu file
   - Giao diện sẽ tự động cập nhật mà không cần F5

## Lưu ý

- **usePolling: true** giúp theo dõi file changes tốt hơn trên một số hệ thống
- **interval: 100** có thể tăng lên (200-500ms) nếu CPU sử dụng quá cao
- **port: 24678** là port riêng cho HMR, tránh xung đột với port chính

## Troubleshooting

Nếu vẫn không hoạt động:

1. **Kiểm tra firewall/antivirus** có block WebSocket connection không
2. **Thử thay đổi HMR port**:
   ```typescript
   hmr: {
     port: 24679, // Thử port khác
     host: 'localhost',
   }
   ```
3. **Tăng polling interval**:
   ```typescript
   watch: {
     usePolling: true,
     interval: 300, // Tăng lên 300ms
   }
   ```

## Kết quả mong đợi
- Thay đổi code sẽ tự động cập nhật giao diện
- Không cần F5 để thấy thay đổi
- React components sẽ preserve state khi có thể
- Console sẽ hiển thị "[vite] hot updated" khi có thay đổi
