import React from 'react';
import { ResponsiveGrid, Skeleton, Card } from '@/shared/components/common';
import OverviewCard from '../OverviewCard/OverviewCard';
import type { ListOverviewCardProps } from '../OverviewCard/OverviewCard.types';

/**
 * Component hiển thị danh sách các OverviewCard với responsive grid
 */
const ListOverviewCard: React.FC<ListOverviewCardProps> = ({
  items,
  maxColumns = { xs: 1, sm: 2, md: 2, lg: 4, xl: 4 },
  gap = 4,
  className,
  isLoading = false,
  skeletonCount = 4,
}) => {
  // Hiển thị skeleton khi loading
  if (isLoading) {
    return (
      <ResponsiveGrid
        maxColumns={maxColumns}
        gap={gap}
        className={className}
      >
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-8 w-16 mb-1" />
            <Skeleton className="h-3 w-20" />
          </Card>
        ))}
      </ResponsiveGrid>
    );
  }

  return (
    <ResponsiveGrid
      maxColumns={maxColumns}
      gap={gap}
      className={className}
    >
      {items.map((item, index) => (
        <OverviewCard
          key={index}
          {...item}
        />
      ))}
    </ResponsiveGrid>
  );
};

export default ListOverviewCard;
