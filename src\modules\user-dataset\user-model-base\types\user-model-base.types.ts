/**
 * Enums cho User Model Base
 */
export enum ModelBaseProvider {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  HUGGINGFACE = 'HUGGINGFACE',
  CUSTOM = 'CUSTOM',
}

export enum ModelBaseStatus {
  AVAILABLE = 'AVAILABLE',
  DEPRECATED = 'DEPRECATED',
  BETA = 'BETA',
  MAINTENANCE = 'MAINTENANCE',
}

export enum ModelBaseType {
  CHAT = 'CHAT',
  COMPLETION = 'COMPLETION',
  EMBEDDING = 'EMBEDDING',
  IMAGE = 'IMAGE',
  AUDIO = 'AUDIO',
  MULTIMODAL = 'MULTIMODAL',
}

export enum UserModelBaseSortBy {
  NAME = 'name',
  PROVIDER = 'provider',
  CREATED_AT = 'createdAt',
  POPULARITY = 'popularity',
}

/**
 * Response cho model base
 */
export interface UserModelBaseResponseDto {
  /**
   * ID của model
   */
  id: string;

  /**
   * Tên model
   */
  name: string;

  /**
   * Tên hiển thị
   */
  displayName: string;

  /**
   * Mô tả model
   */
  description: string;

  /**
   * Nhà cung cấp
   */
  provider: ModelBaseProvider;

  /**
   * Loại model
   */
  type: ModelBaseType;

  /**
   * Trạng thái
   */
  status: ModelBaseStatus;

  /**
   * Số token tối đa
   */
  maxTokens: number;

  /**
   * Chi phí per token (input)
   */
  costPerInputToken: number;

  /**
   * Chi phí per token (output)
   */
  costPerOutputToken: number;

  /**
   * Đơn vị tiền tệ
   */
  currency: string;

  /**
   * Có hỗ trợ fine-tuning không
   */
  supportsFineTuning: boolean;

  /**
   * Có hỗ trợ function calling không
   */
  supportsFunctionCalling: boolean;

  /**
   * Có hỗ trợ vision không
   */
  supportsVision: boolean;

  /**
   * Thời gian tạo
   */
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  updatedAt: number;

  /**
   * Số lần sử dụng
   */
  usageCount: number;

  /**
   * Đánh giá trung bình
   */
  averageRating: number;
}

/**
 * Response chi tiết cho model base
 */
export interface UserModelBaseDetailResponseDto extends UserModelBaseResponseDto {
  /**
   * Thông số kỹ thuật chi tiết
   */
  specifications: {
    architecture: string;
    trainingData: string;
    languages: string[];
    capabilities: string[];
    limitations: string[];
  };

  /**
   * Cấu hình mặc định
   */
  defaultConfig: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
  };

  /**
   * Ví dụ sử dụng
   */
  examples: Array<{
    title: string;
    description: string;
    input: string;
    output: string;
    config?: Record<string, unknown>;
  }>;

  /**
   * Thống kê hiệu suất
   */
  performanceStats: {
    averageResponseTime: number;
    successRate: number;
    errorRate: number;
    throughput: number;
  };

  /**
   * Changelog
   */
  changelog: Array<{
    version: string;
    date: string;
    changes: string[];
  }>;
}

/**
 * Query DTO cho model base
 */
export interface UserModelBaseQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Tìm kiếm theo nhà cung cấp
   */
  provider?: ModelBaseProvider;

  /**
   * Tìm kiếm theo loại model
   */
  type?: ModelBaseType;

  /**
   * Tìm kiếm theo trạng thái
   */
  status?: ModelBaseStatus;

  /**
   * Chỉ hiển thị models hỗ trợ fine-tuning
   */
  supportsFineTuning?: boolean;

  /**
   * Chỉ hiển thị models hỗ trợ function calling
   */
  supportsFunctionCalling?: boolean;

  /**
   * Chỉ hiển thị models hỗ trợ vision
   */
  supportsVision?: boolean;

  /**
   * Trường sắp xếp
   */
  sortBy?: UserModelBaseSortBy;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO cho việc so sánh models
 */
export interface CompareModelsDto {
  /**
   * Danh sách ID models cần so sánh
   */
  modelIds: string[];
}

/**
 * Response cho việc so sánh models
 */
export interface CompareModelsResponseDto {
  /**
   * Thông tin các models
   */
  models: UserModelBaseDetailResponseDto[];

  /**
   * Bảng so sánh
   */
  comparison: {
    features: Array<{
      name: string;
      values: Array<{
        modelId: string;
        value: string | number | boolean;
        score?: number;
      }>;
    }>;
    
    performance: Array<{
      metric: string;
      values: Array<{
        modelId: string;
        value: number;
        unit: string;
      }>;
    }>;

    cost: Array<{
      scenario: string;
      values: Array<{
        modelId: string;
        cost: number;
        currency: string;
      }>;
    }>;
  };

  /**
   * Khuyến nghị
   */
  recommendations: Array<{
    useCase: string;
    recommendedModelId: string;
    reason: string;
    score: number;
  }>;
}

/**
 * DTO cho việc đánh giá model
 */
export interface RateModelDto {
  /**
   * Điểm đánh giá (1-5)
   */
  rating: number;

  /**
   * Nhận xét
   */
  comment?: string;

  /**
   * Use case
   */
  useCase?: string;
}

/**
 * Response cho việc đánh giá model
 */
export interface RateModelResponseDto {
  /**
   * Thành công
   */
  success: boolean;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Điểm đánh giá mới
   */
  newAverageRating: number;

  /**
   * Tổng số đánh giá
   */
  totalRatings: number;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
