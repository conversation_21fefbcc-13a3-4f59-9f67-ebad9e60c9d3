/**
 * Agent Configuration Layout Component
 * S<PERSON> dụng XState để quản lý layout state và responsive design
 */

import React, { useMemo } from 'react';
import { useAgentConfigLayout } from '../../hooks/useAgentConfigLayout';
import { TypeAgentConfig } from '../../types';
import { cn } from '@/shared/utils/cn';
import AgentConfigAccordionProvider from '../../contexts/AgentConfigAccordionContext.tsx';

interface AgentConfigLayoutProps {
  typeAgentConfig: TypeAgentConfig;
  children: {
    leftPanel: React.ReactNode;
    // ModelConfig đã được chuyển vào leftPanel
    profileConfig?: React.ReactNode;
    integrationConfig?: React.ReactNode;
    strategyConfig?: React.ReactNode;
    responseConfig?: React.ReactNode;
    convertConfig?: React.ReactNode;
    multiAgentConfig?: React.ReactNode;
  };
}

/**
 * Component renderer dựa trên layout state
 */
const ComponentRenderer: React.FC<{
  componentName: string;
  children: React.ReactNode;
  isVisible: boolean;
}> = ({ componentName, children, isVisible }) => {
  if (!isVisible) return null;

  return (
    <div
      className="w-full transition-all duration-200 ease-in-out hover:transform hover:-translate-y-0.5 focus-within:ring-2 focus-within:ring-primary/20"
      data-component={componentName}
    >
      {children}
    </div>
  );
};

/**
 * Layout chính cho Agent Configuration
 */
export const AgentConfigLayout: React.FC<AgentConfigLayoutProps> = ({
  typeAgentConfig,
  children
}) => {
  const {
    layoutMode,
    isComponentVisible,
    breakpoint,
  } = useAgentConfigLayout(typeAgentConfig);

  // Generate layout classes using Tailwind
  const layoutClasses = useMemo(() => {
    const baseClasses = 'w-full transition-all duration-300 ease-in-out';

    const modeClasses = {
      'EMPTY': 'layout-empty',
      'LEFT_ONLY': 'layout-left-only',
      'RIGHT_ONLY': 'layout-right-only',
      'RIGHT_HEAVY': 'layout-right-heavy',
      'BALANCED': 'layout-balanced'
    };

    const breakpointClasses = {
      'mobile': 'layout-mobile',
      'tablet': 'layout-tablet',
      'desktop': 'layout-desktop'
    };

    return cn(baseClasses, modeClasses[layoutMode], breakpointClasses[breakpoint]);
  }, [layoutMode, breakpoint]);

  // Phân loại components theo cột - ModelConfig đã được loại bỏ vì ở header
  const leftComponents = useMemo(() => {
    const components = [];

    // IntegrationConfig - ưu tiên cột trái
    if (isComponentVisible('IntegrationConfig') && children.integrationConfig) {
      components.push({
        name: 'IntegrationConfig',
        component: children.integrationConfig
      });
    }

    // StrategyConfig - ưu tiên cột trái
    if (isComponentVisible('StrategyConfig') && children.strategyConfig) {
      components.push({
        name: 'StrategyConfig',
        component: children.strategyConfig
      });
    }

        // ConvertConfig - ưu tiên cột phải
    if (isComponentVisible('ConvertConfig') && children.convertConfig) {
      components.push({
        name: 'ConvertConfig',
        component: children.convertConfig
      });
    }

    return components;
  }, [isComponentVisible, children]);

  const rightComponents = useMemo(() => {
    const components = [];

    // ProfileConfig - ưu tiên cột phải
    if (isComponentVisible('ProfileConfig') && children.profileConfig) {
      components.push({
        name: 'ProfileConfig',
        component: children.profileConfig
      });
    }

    // ResponseConfig - ưu tiên cột phải
    if (isComponentVisible('ResponseConfig') && children.responseConfig) {
      components.push({
        name: 'ResponseConfig',
        component: children.responseConfig
      });
    }

    // MultiAgentConfig - ưu tiên cột phải
    if (isComponentVisible('MultiAgentConfig') && children.multiAgentConfig) {
      components.push({
        name: 'MultiAgentConfig',
        component: children.multiAgentConfig
      });
    }

    return components;
  }, [isComponentVisible, children]);





  // Render single column for mobile
  const renderMobileLayout = () => (
    <div className="space-y-6">
      {/* Left Panel on mobile */}
      <div className="w-full">
        {children.leftPanel}
      </div>

      {/* All components stacked */}
      {[...leftComponents, ...rightComponents].map(({ name, component }) => (
        <ComponentRenderer
          key={name}
          componentName={name}
          isVisible={true}
          children={component}
        />
      ))}
    </div>
  );

  return (
    <AgentConfigAccordionProvider>
      <div className={layoutClasses}>
        {layoutMode === 'EMPTY' ? (
          <div className="text-center py-12 text-muted-foreground">
            Không có cấu hình nào để hiển thị
          </div>
        ) : breakpoint === 'mobile' ? (
          renderMobileLayout()
        ) : (
          // Desktop/Tablet: 2 columns equal width (50-50)
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Left Panel - Equal Width */}
            <div className="w-full">
              {children.leftPanel}
            </div>

            {/* Right Content - Equal Width */}
            <div className="w-full">
              {[...leftComponents, ...rightComponents].length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  Không có cấu hình nào để hiển thị
                </div>
              ) : (
                <div className="space-y-6">
                  {[...leftComponents, ...rightComponents].map(({ name, component }) => (
                    <ComponentRenderer
                      key={name}
                      componentName={name}
                      isVisible={true}
                      children={component}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </AgentConfigAccordionProvider>
  );
};
