import { apiClient } from '@/shared/api/axios';
import {
  CreateModelFineTuneFromDatasetDto,
  CheckCompatibilityDto,
  TestModelDto,
  UserModelFineTuneResponseDto,
  UserModelFineTuneDetailResponseDto,
  UserModelFineTuneQueryDto,
  FineTuneHistoryResponseDto,
  FineTuneStatusResponseDto,
  CompatibilityCheckResponseDto,
  TestModelResponseDto,
  PaginatedResult,
} from '../types/user-model-fine-tune.types';

const API_BASE_URL = '/user/model-fine-tune';

/**
 * Tạo model fine tune từ dataset
 * @param data Dữ liệu để tạo model fine tune
 * @returns Thông tin model fine tune đã tạo
 */
export const createModelFineTuneFromDataset = async (
  data: CreateModelFineTuneFromDatasetDto
): Promise<UserModelFineTuneDetailResponseDto> => {
  const response = await apiClient.post<UserModelFineTuneDetailResponseDto>(
    `${API_BASE_URL}/from-dataset`,
    data
  );
  return response.result;
};

/**
 * Kiểm tra tương thích giữa dataset và base model
 * @param data Dữ liệu để kiểm tra tương thích
 * @returns Kết quả kiểm tra tương thích
 */
export const checkCompatibility = async (
  data: CheckCompatibilityDto
): Promise<CompatibilityCheckResponseDto> => {
  const response = await apiClient.post<CompatibilityCheckResponseDto>(
    `${API_BASE_URL}/check-compatibility`,
    data
  );
  return response.result;
};

/**
 * Lấy danh sách model fine tune của user có phân trang
 * @param queryDto Tham số truy vấn
 * @returns Danh sách model fine tune với phân trang
 */
export const getUserModelFineTuneList = async (
  queryDto?: UserModelFineTuneQueryDto
): Promise<PaginatedResult<UserModelFineTuneResponseDto>> => {
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.status) queryParams.append('status', queryDto.status);
  if (queryDto?.baseModelName) queryParams.append('baseModelName', queryDto.baseModelName);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedResult<UserModelFineTuneResponseDto>>(url);
  return response.result;
};

/**
 * Lấy chi tiết model fine tune
 * @param id ID của model fine tune
 * @returns Thông tin chi tiết model fine tune
 */
export const getUserModelFineTuneDetail = async (
  id: string
): Promise<UserModelFineTuneDetailResponseDto> => {
  const response = await apiClient.get<UserModelFineTuneDetailResponseDto>(
    `${API_BASE_URL}/${id}`
  );
  return response.result;
};

/**
 * Lấy lịch sử fine tune
 * @param id ID của model fine tune
 * @returns Lịch sử fine tune
 */
export const getFineTuneHistory = async (
  id: string
): Promise<FineTuneHistoryResponseDto[]> => {
  const response = await apiClient.get<FineTuneHistoryResponseDto[]>(
    `${API_BASE_URL}/${id}/history`
  );
  return response.result;
};

/**
 * Lấy trạng thái fine tune
 * @param id ID của model fine tune
 * @returns Trạng thái fine tune
 */
export const getFineTuneStatus = async (
  id: string
): Promise<FineTuneStatusResponseDto> => {
  const response = await apiClient.get<FineTuneStatusResponseDto>(
    `${API_BASE_URL}/${id}/status`
  );
  return response.result;
};

/**
 * Hủy fine tune
 * @param id ID của model fine tune
 * @returns Kết quả hủy
 */
export const cancelFineTune = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.patch<{ success: boolean }>(
    `${API_BASE_URL}/${id}/cancel`
  );
  return response.result;
};

/**
 * Xóa model fine tune
 * @param id ID của model fine tune
 * @returns Kết quả xóa
 */
export const deleteUserModelFineTune = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(
    `${API_BASE_URL}/${id}`
  );
  return response.result;
};

/**
 * Test model fine tune
 * @param id ID của model fine tune
 * @param data Dữ liệu test
 * @returns Kết quả test
 */
export const testModel = async (
  id: string,
  data: TestModelDto
): Promise<TestModelResponseDto> => {
  const response = await apiClient.post<TestModelResponseDto>(
    `${API_BASE_URL}/${id}/test`,
    data
  );
  return response.result;
};

/**
 * Lấy danh sách base models có sẵn
 * @returns Danh sách base models
 */
export const getAvailableBaseModels = async (): Promise<string[]> => {
  const response = await apiClient.get<string[]>(`${API_BASE_URL}/base-models`);
  return response.result;
};

/**
 * Lấy thông tin chi tiết base model
 * @param modelName Tên base model
 * @returns Thông tin chi tiết base model
 */
export const getBaseModelInfo = async (modelName: string): Promise<{
  name: string;
  description: string;
  capabilities: string[];
  maxTokens: number;
  costPerToken: number;
}> => {
  const response = await apiClient.get<{
    name: string;
    description: string;
    capabilities: string[];
    maxTokens: number;
    costPerToken: number;
  }>(`${API_BASE_URL}/base-models/${encodeURIComponent(modelName)}`);
  return response.result;
};

/**
 * Ước tính chi phí fine tune
 * @param datasetId ID của dataset
 * @param baseModelName Tên base model
 * @returns Ước tính chi phí
 */
export const estimateFineTuneCost = async (
  datasetId: string,
  baseModelName: string
): Promise<{
  estimatedCost: number;
  estimatedTime: number;
  currency: string;
  breakdown: {
    trainingCost: number;
    storageCost: number;
    inferenceCost: number;
  };
}> => {
  const response = await apiClient.post<{
    estimatedCost: number;
    estimatedTime: number;
    currency: string;
    breakdown: {
      trainingCost: number;
      storageCost: number;
      inferenceCost: number;
    };
  }>(`${API_BASE_URL}/estimate-cost`, {
    datasetId,
    baseModelName,
  });
  return response.result;
};

/**
 * Validate model name
 * @param name Tên model cần validate
 * @returns Kết quả validation
 */
export const validateModelName = (name: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!name || name.trim().length === 0) {
    errors.push('Tên model không được để trống');
  }

  if (name.length > 255) {
    errors.push('Tên model không được vượt quá 255 ký tự');
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(name)) {
    errors.push('Tên model chỉ được chứa chữ cái, số, dấu gạch dưới và dấu gạch ngang');
  }

  if (name.startsWith('-') || name.endsWith('-')) {
    errors.push('Tên model không được bắt đầu hoặc kết thúc bằng dấu gạch ngang');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Format training time for display
 * @param seconds Thời gian tính bằng giây
 * @returns Chuỗi hiển thị thời gian
 */
export const formatTrainingTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)} giây`;
  }

  if (seconds < 3600) {
    const minutes = Math.round(seconds / 60);
    return `${minutes} phút`;
  }

  if (seconds < 86400) {
    const hours = Math.round(seconds / 3600);
    return `${hours} giờ`;
  }

  const days = Math.round(seconds / 86400);
  return `${days} ngày`;
};
